# Documentação das User Functions e Classes - Pasta Comissões

## Resumo
Esta documentação apresenta todas as User Functions e Classes encontradas na pasta `adm_vendas/Comissões` e suas respectivas subpastas.

## Estatísticas
- **Total de User Functions:** 600+ funções
- **Total de Classes:** 22 classes
- **Principais subpastas:** Environment Definition, Interfaces, RV, Relatorios, Rotinas Ajustes, Webservices

## Tabela de User Functions e Classes

| Arquivo | Função/Classe | Patch | Descrição |
|---------|---------------|-------|-----------|
| ACCSTA07.PRW | ACCSTA07 | protheus_br/adm_vendas/Comissões | Refaz a provisao para o titulo + parcela selecionado |
| ACCSTA07.PRW | AC07REPR | protheus_br/adm_vendas/Comissões | Re-processamento de provisões |
| ACCSTA07.PRW | BKSTATUS1 | protheus_br/adm_vendas/Comissões | Backup de status |
| ACCSTA09.PRW | ACCSTA09 | protheus_br/adm_vendas/Comissões | Processamento de itens de contrato |
| ACCSTA10.PRW | ACCSTA10 | protheus_br/adm_vendas/Comissões | Rotina de processamento de comissões |
| ACCSTA24.PRW | ACCSTA24 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA24.PRW | VlParBox | protheus_br/adm_vendas/Comissões | Validação de parâmetros |
| ACCSTA30.PRW | ACCSTA30 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA30.PRW | AC30Filt | protheus_br/adm_vendas/Comissões | Filtro de dados |
| ACCSTA30.PRW | AC30DtCh | protheus_br/adm_vendas/Comissões | Validação de data |
| ACCSTA33.PRW | ACCSTA33 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA33.PRW | ZPJLegenda | protheus_br/adm_vendas/Comissões | Legenda para ZPJ |
| ACCSTA33.PRW | ZPJGravLog | protheus_br/adm_vendas/Comissões | Gravação de log ZPJ |
| ACCSTA33.PRW | VfyNotas | protheus_br/adm_vendas/Comissões | Verificação de notas |
| ACCSTA35.PRW | ACCSTA35 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA35.PRW | ACSTA35I | protheus_br/adm_vendas/Comissões | Inclusão |
| ACCSTA35.PRW | ACSTA35A | protheus_br/adm_vendas/Comissões | Alteração |
| ACCSTA35.PRW | ACSTA35B | protheus_br/adm_vendas/Comissões | Browse |
| ACCSTA35.PRW | ACSTA35L | protheus_br/adm_vendas/Comissões | Listagem |
| ACCSTA36.PRW | ACCSTA36 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA36.PRW | ACSTA36E | protheus_br/adm_vendas/Comissões | Execução |
| ACCSTA53.PRW | ACCSTA53 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA54.prw | ACCSTA54 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA54.prw | ACCSTA5A | protheus_br/adm_vendas/Comissões | Função A |
| ACCSTA54.prw | ACCRELAG | protheus_br/adm_vendas/Comissões | Relatório |
| ACCSTA54.prw | ACCSTA5B | protheus_br/adm_vendas/Comissões | Função B |
| ACCSTA54.prw | ACCSTA5C | protheus_br/adm_vendas/Comissões | Função C |
| ACCSTA54.prw | ACCSTA5D | protheus_br/adm_vendas/Comissões | Função D |
| ACCSTA55.prw | ACCSTA55 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA55.prw | TOTTXL1 | protheus_br/adm_vendas/Comissões | Total Excel |
| ACCSTA55.prw | CcBloqAlt | protheus_br/adm_vendas/Comissões | Bloqueio de alteração |
| ACCSTA56.prw | ACCSTA56 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA56.prw | ECTBCOMIS | protheus_br/adm_vendas/Comissões | Contabilização de comissões |
| ACCSTA56.prw | ESTCOM2 | protheus_br/adm_vendas/Comissões | Estorno de comissões |
| ACCSTA57.PRW | ACCSTA57 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA57.PRW | ACCSTA5G | protheus_br/adm_vendas/Comissões | Função G |
| ACCSTA58.prw | ACCSTA58 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA58.prw | MNCSTA58 | protheus_br/adm_vendas/Comissões | Manutenção |
| ACCSTA60.PRW | ACCSTA60 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA60.PRW | AcolsCli | protheus_br/adm_vendas/Comissões | Acols de cliente |
| ACCSTA60.PRW | Fat126ZX | protheus_br/adm_vendas/Comissões | Faturamento |
| ACCSTA60.PRW | fCanDevP | protheus_br/adm_vendas/Comissões | Cancelamento de devolução |
| ACCSTA63.prw | A63GERAZS6 | protheus_br/adm_vendas/Comissões | Geração ZS6 |
| ACCSTA64.prw | ACCSTA64 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA64.prw | VWTA64A | protheus_br/adm_vendas/Comissões | View A |
| ACCSTA64.prw | VWTA64B | protheus_br/adm_vendas/Comissões | View B |
| ACCSTA64.prw | VWTA64C | protheus_br/adm_vendas/Comissões | View C |
| ACCSTABX.PRW | ACCSTABX | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTABX.PRW | ACSTABXC | protheus_br/adm_vendas/Comissões | Função C |
| ACRM095.prw | ACRM095 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACRM095.prw | ACRM095A | protheus_br/adm_vendas/Comissões | Função A |
| ACRM095.prw | ACRM95VLD | protheus_br/adm_vendas/Comissões | Validação |
| ACRM095.prw | ACRM95TOK | protheus_br/adm_vendas/Comissões | Token |
| ACRM900.PRW | ACRM900 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACRM900.PRW | A900TpAp | protheus_br/adm_vendas/Comissões | Tipo de aprovação |
| ACRM900.PRW | A900JOB | protheus_br/adm_vendas/Comissões | Job |
| ADKPIC.prw | ADKPIC | protheus_br/adm_vendas/Comissões | Rotina ADK |
| CADZB4.prw | CADZB4 | protheus_br/adm_vendas/Comissões | Cadastro ZB4 |
| COMSM22.prw | COMSM22 | protheus_br/adm_vendas/Comissões | Comissões M22 |
| COMSM23.prw | COMSM23 | protheus_br/adm_vendas/Comissões | Comissões M23 |
| COMSM24.prw | COMSM24 | protheus_br/adm_vendas/Comissões | Comissões M24 |
| COMSM24.prw | COMSM24B | protheus_br/adm_vendas/Comissões | Função B |
| COMSM24.prw | COMSM24C | protheus_br/adm_vendas/Comissões | Função C |
| COMSM24.prw | COMSM24D | protheus_br/adm_vendas/Comissões | Função D |
| COMSM24.prw | ImportaExcel | protheus_br/adm_vendas/Comissões | Importação Excel |
| COMSM24.prw | ImpTabel | protheus_br/adm_vendas/Comissões | Importação de tabela |
| COMSM24.prw | InclusaoModel | protheus_br/adm_vendas/Comissões | Inclusão modelo |

## Classes Principais

| Arquivo | Classe | Patch | Descrição |
|---------|--------|-------|-----------|
| cmsbaixa.prw | CMSBaixa | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Objeto responsável pela criação do objeto de baixas do título para cálculo de Previsão |
| cmsbxliq.prw | CMSBxLiq | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe para baixas líquidas |
| cmscfp.prw | CMSCFP | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe CFP |
| cmscliente.prw | CMSCliente | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de cliente |
| cmsitmcont.prw | CMSItmCont | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de contrato |
| cmsitmnfis.prw | CMSItmNFis | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de nota fiscal |
| cmsitmped.prw | CMSItmPed | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de pedido |
| cmsmsgproc.prw | CMSMsgProc | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de processamento de mensagens |
| cmsnfiscal.prw | CMSNfiscal | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de nota fiscal |
| cmsoportu.prw | CMSOportu | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de oportunidade |
| cmsproduto.prw | CMSProduto | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de produto |
| cmspropost.prw | CMSPropost | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de proposta |
| cmsterrit.prw | CMSTerrit | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de território |
| cmstitulo.prw | CMSTitulo | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de título |
| cmstmvend.prw | CMSTmVend | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de time de vendas |
| ACSTA44CLS.prw | ACSTA44CLS | protheus_br/adm_vendas/Comissões/Interfaces/Classes | Classe ACSTA44 |
| TdiCrtView.PRW | TdiCrtView | protheus_br/adm_vendas/Comissões/Interfaces/Classes | Classe de criação de view |
| TIRVACTIO.PRW | TIRVACTIO | protheus_br/adm_vendas/Comissões/RV | Classe para integração com ACTIO |
| TDIFILTER.PRW | TDIFILTER | protheus_br/adm_vendas/Comissões | Classe de filtro TDI |

## Subpasta RV (Remuneração Variável)

### User Functions Principais - RV
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TIRVJ000.PRW | TIRVJ000 | 12.23.10 | Job responsável por buscar os dados do PSA e Calcular na P66 |
| TIRVJ000.PRW | TIRVARQ | 12.1.2310 | Envio de arquivos Agrupados para a Actio |
| TIRVF000.PRW | TIRVF000 | 1.0 | Callback de autenticação rv customizado |
| TIRVF002.PRW | TIRVF002 | 1.0 | Callback de autenticação PSA ACTIO |
| TIRVF005.prw | TIRVF005 | 12.1.33 | Função Callback de cadastro de Macro Areas x Divisao x Cargos P65 vinda do RM |
| TIRVJ008.PRW | TIRVJ008 | 1.0 | Job Responsável por criar a P37 para consultar a API do PSA View PAPEIS PSA |
| TIRVJ010.PRW | TIRVJ010 | 1.0 | Job Responsável por criar a P37 para consultar a API do PSA View Escopo fechado PSA |
| TIRVF014.PRW | TIRVF014 | 1.0 | Callback de VIEW INFORMACOES PSA ACTIO - CUBO SERVICOS |
| TIRVF015.PRW | TIRVF015 | 1.0 | Callback de VIEW INFORMACOES PSA ACTIO - CUBO SERVICOS - Horas e Material |
| TIRVA001.prw | TIRVA001 | protheus_br/adm_vendas/Comissões/RV | Rotina principal de remuneração variável |
| TIRVA002.prw | TIRVA002 | protheus_br/adm_vendas/Comissões/RV | Cadastro de áreas |
| TIRVA003.prw | TIRVA003 | protheus_br/adm_vendas/Comissões/RV | Cadastro de macro áreas |
| TIRVA004.prw | TIRVA004 | protheus_br/adm_vendas/Comissões/RV | Cadastro de elegíveis |
| TIRVA005.prw | TIRVA005 | protheus_br/adm_vendas/Comissões/RV | Cadastro de indicadores |

### Funções de Importação e Utilitários
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TGetImpFile.prw | TGetImpF | protheus_br/adm_vendas/Comissões | Importação de arquivo CSV |
| TGetImpFile.prw | Str2Arr | protheus_br/adm_vendas/Comissões | Conversão de string para array |
| TMntProv.prw | TMnt01 | protheus_br/adm_vendas/Comissões | Manutenção de provisões |
| TMntProv.prw | TMntProv | protheus_br/adm_vendas/Comissões | Manutenção de provisões principal |
| TLogAProv.prw | LogAprov | protheus_br/adm_vendas/Comissões | Log de aprovações |
| TDIATUCMS.PRW | TDIATUCMS | protheus_br/adm_vendas/Comissões | Atualização diária CMS |
| tdi_faixar.prw | TDI_FAIXAR | protheus_br/adm_vendas/Comissões | Faixas TDI |
| tdi_faixar.prw | TDI_RJ_COM | protheus_br/adm_vendas/Comissões | Comissões RJ |

### Webservices
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TCMSS001.prw | TCMSS001 | protheus_br/adm_vendas/Comissões/Webservices | Webservice principal |
| TCMSS001.prw | AtualPRU | protheus_br/adm_vendas/Comissões/Webservices | Atualização PRU |
| TCMSS001.prw | ContAces | protheus_br/adm_vendas/Comissões/Webservices | Controle de acesso |
| TCMSS003.PRW | TCMSS003 | protheus_br/adm_vendas/Comissões/Webservices | Webservice 003 |

### Relatórios
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| ACCSTR02.PRW | ACCSTR02 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 02 |
| ACCSTR39.prw | ACCSTR39 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 39 |
| ACCSTR45.prw | ACCSTR45 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 45 |
| CMSR010.prw | CMSR010 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 010 |
| CMSR011.prw | CMSR011 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 011 |
| CMSR080.PRW | CMSR080 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 080 |
| CMSR090.prw | CMSR090 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 090 |
| CMSR100.prw | CMSR100 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 100 |
| CMSR110.prw | CMSR110 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 110 |
| CMSR111.prw | CMSR111 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 111 |
| CMSR120.PRW | CMSR120 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 120 |
| CMSR400.PRW | CMSR400 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 400 |
| CMSR501.PRW | CMSR501 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 501 |
| TCMSR001.PRW | TCMSR001 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório TCMS 001 |
| TCMSR001.PRW | RelComis | protheus_br/adm_vendas/Comissões/Relatorios | Relatório de comissões |
| TCMSR002.PRW | TCMSR002 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório TCMS 002 |

### Rotinas de Ajuste
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| APEXPLORER.PRW | ApExplorer | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Explorer de aplicações |
| CMSAJU01.prw | CMSAJU01 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 01 |
| CMSAJU01.prw | CMSEMAIL | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Envio de email |
| CMSAJU01.prw | CMSACENT | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Acentuação |
| CMSAJU01.prw | SIMMail | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Envio de email SIM |
| CMSAJU02.PRW | CMSAJU02 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 02 |
| CMSAJU03.PRW | CMSAJU03 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 03 |
| CMSAJU04.prw | CMSAJU04 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 04 |
| CMSAJU05.PRW | CMSAJU05 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 05 |
| IMPORTCSV.PRW | ImportCSV | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Importação CSV |

### Procedures
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| CMPRC001.prw | CMPRC001 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 001 |
| CMPRC002.prw | CMPRC002 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 002 |
| CMPRC005.prw | CMPRC005 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 005 |
| CMPRC009.prw | CMPRC009 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 009 |

### Funções Genéricas
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| CMSXFUN.prw | CMGerX5T | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Geração X5T |
| CMSXFUN.prw | CMGerPBL | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Geração PBL |
| CMSXFUN.prw | CMRetApro | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Retorno de aprovação |
| CRMImportTer.prw | TerImport | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Importação de território |
| MXMECM.prw | JobECMX | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Job ECM |
| TINTJ018.PRW | TINTJ018 | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Integração 018 |

### Environment Definition Functions
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| accsta01.prw | ACCSTA01 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Cálculo de provisão de Comissão |
| accsta13.prw | ACCSTA13 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Agendamento da provisão de comissão |
| accsta13.prw | ACSTA13C | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Processamento por thread |
| accsta13.prw | ValDupAg | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Validação de duplicação de agendamento |
| accsta13.prw | AtuZPJ | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Atualização ZPJ |
| accsta13.prw | VerAgDat | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificação de agendamento por data |
| accsta13.prw | ACC13LgErr | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Log de erro |
| accsta13.prw | ACSTA13D | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Processamento distribuído |
| accsta13.prw | STJOBCMS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Status job CMS |
| accsta13.prw | QuebraNf | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Quebra de nota fiscal |
| accsta13.prw | CALCDIAR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Cálculo diário |
| accsta13.prw | DELZCC | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Exclusão ZCC |
| accstxfcmp.prw | ExecModApu | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Executa Modelo de Apuração da Campanha |
| accstxfcmp.prw | ExecArcor | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Execução Arcor |

### Funções Auxiliares (accstafunn.prw)
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| accstafunn.prw | C01ATRASO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Cálculo de atraso |
| accstafunn.prw | fContrato | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Função de contrato |
| accstafunn.prw | fChk180Pg | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificação 180 páginas |
| accstafunn.prw | fHaveCP | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificação contas a pagar |
| accstafunn.prw | fGetPTerri | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter território |
| accstafunn.prw | fTerFaixa | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Território por faixa |
| accstafunn.prw | fCdu2010 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | CDU 2010 |
| accstafunn.prw | fSMS2010 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | SMS 2010 |
| accstafunn.prw | FUNSCS2010 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | SCS 2010 |
| accstafunn.prw | FAIXAATR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Faixa de atraso |
| accstafunn.prw | fRetComi | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Retorno de comissão |
| accstafunn.prw | TMVByPrpst | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time de vendas por proposta |
| accstafunn.prw | TMVByCFP | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time de vendas por CFP |
| accstafunn.prw | TMVByCli | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time de vendas por cliente |
| accstafunn.prw | TMVByZAY | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time de vendas por ZAY |
| accstafunn.prw | TMVAddTerr | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar território ao time de vendas |
| accstafunn.prw | TMVAddParc | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar parceiro |
| accstafunn.prw | FANTEIMPOS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Antecipação de impostos |
| accstafunn.prw | FUNPCOMPAD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual comissão padrão |
| accstafunn.prw | GETPCOMZD1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter percentual comissão ZD1 |
| accstafunn.prw | FTIPCOM001 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão 001 |
| accstafunn.prw | RECTPCPAD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo comissão padrão |
| accstafunn.prw | RECTIPCCDU | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo comissão CDU |
| accstafunn.prw | TIPCOMP1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão P1 |
| accstafunn.prw | TIPCOMP2 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão P2 |
| accstafunn.prw | TIPCOMP3 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão P3 |
| accstafunn.prw | FISBLINE | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Linha de negócio |
| accstafunn.prw | CUSTOINFRA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Custo infraestrutura |
| accstafunn.prw | ISPERCME | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual ME |
| accstafunn.prw | FUNPERCZAY | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual ZAY |
| accstafunn.prw | FVALCDUTRC | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Valor CDU TRC |
| accstafunn.prw | FBASETROCA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Base de troca |
| accstafunn.prw | STATUSTROC | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Status de troca |
| accstafunn.prw | FVALBAIXAS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Valor de baixas |
| accstafunn.prw | PBASEBAIXA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual base baixa |
| accstafunn.prw | BAIXAPEND1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Baixa pendente 1 |
| accstafunn.prw | BAIXA90DIA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Baixa 90 dias |
| accstafunn.prw | FGERAABATM | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Gerar abatimento |
| accstafunn.prw | SMSGRETERR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | SMS gerente território |
| accstafunn.prw | REMTERRIRM | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Remover território IRM |
| accstafunn.prw | CHK180PEND | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar 180 pendente |
| accstafunn.prw | FABATSALDO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Abater saldo |
| accstafunn.prw | fGrvLogMem | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Gravar log em memória |
| accstafunn.prw | FGETZD8TER | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZD8 território |
| accstafunn.prw | ISEXCEPTIO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar exceção |
| accstafunn.prw | FGETZS6TER | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZS6 território |
| accstafunn.prw | ISEXCLUT | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar exclusão |
| accstafunn.prw | FADDTERRIT | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar território |
| accstafunn.prw | FCALCFATOR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Calcular fator |
| accstafunn.prw | FGETFATOR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter fator |
| accstafunn.prw | FGETVALZD2 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter valor ZD2 |
| accstafunn.prw | FISPREMEAR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar prêmio AR |
| accstafunn.prw | FISTRIMEST | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar trimestre |
| accstafunn.prw | CMSGrvHist | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Gravar histórico CMS |
| accstafunn.prw | FISARRECIS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar arrecadação |
| accstafunn.prw | FCATCLIMDB | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Categoria cliente MDB |
| accstafunn.prw | FADDHUBAD2 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar hub AD2 |
| accstafunn.prw | TIPCOMSCS1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão SCS1 |
| accstafunn.prw | FEXNMETAOK | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar meta OK |
| accstafunn.prw | FGETSEGTER | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter segundo território |
| accstafunn.prw | ISEXCLUSEG | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar exclusão segundo |
| accstafunn.prw | SaldoZXO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Saldo ZXO |
| accstafunn.prw | FZXOSTATUS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Status ZXO |
| accstafunn.prw | FUNPSATCLI | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | PSA cliente |
| accstafunn.prw | BX180DIAS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Baixa 180 dias |
| accstafunn.prw | RECTIPCF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo CF20 |
| accstafunn.prw | RECTSPF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar SP F20 |
| accstafunn.prw | RTIPCDUF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo CDU F20 |
| accstafunn.prw | FOVER2015 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Over 2015 |
| accstafunn.prw | FANTIMP02 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Antecipação imposto 02 |
| accstafunn.prw | FROTOVER | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Rotina over |
| accstafunn.prw | FROTBASEL | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Rotina base L |
| accstafunn.prw | RECTIPCINT | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo C INT |
| accstafunn.prw | TMVAddOfer | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar oferta ao time de vendas |
| accstafunn.prw | FBSLPL5 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Base PL5 |
| accstafunn.prw | FBSLZXO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Base ZXO |
| accstafunn.prw | CMSBSL001 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Base CMS 001 |
| accstafunn.prw | FUNPZD12 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual ZD12 |
| accstafunn.prw | RECNEWF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar new F20 |
| accstafunn.prw | GETCMSVD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter CMS vendedor |
| accstafunn.prw | EXMODBXVD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Executar modelo baixa vendedor |
| accstafunn.prw | RECTPCPCS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo PCS |
| accstafunn.prw | FUNPZD13 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual ZD13 |
| accstafunn.prw | FGETZD8ARG | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZD8 Argentina |
| accstafunn.prw | RTIPCOMF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão F20 |
| accstafunn.prw | GETPCOMMES | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter percentual comissão mês |
| accstafunn.prw | TMVGrpCli | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time vendas grupo cliente |
| accstafunn.prw | RECTIPCPAD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo comissão padrão |
| accstafunn.prw | CtrUsrRot | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Controle usuário rotina |
| accstafunn.prw | LockByTI | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Lock por TI |
| accstafunn.prw | UnLockTI | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Unlock TI |
| accstafunn.prw | GetAcesso | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter acesso |
| accstafunn.prw | GetDadEmp | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter dados empresa |
| accstafunn.prw | CRFUN01Z | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Criar função 01Z |
| accstafunn.prw | Arr2Str | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Array para string |
| accstafunn.prw | fGetComVal | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter valor comissão |
| accstafunn.prw | fGetZD1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZD1 |
| accstafunn.prw | TRecLog | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Log de registro |
| accstafunn.prw | fZD1FAT | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | ZD1 faturamento |
| accstafunn.prw | COMREVEN | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Comissão receita |
| accstafunn.prw | FROTCON5 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Rotina CON5 |
| accstafunn.prw | VFYVLTAB | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar valor tabela |
| accstafunn.prw | TIPCOM5 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão 5 |
| accstafunn.prw | fGetZD1C | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZD1C |
| accstafunn.prw | fGetCusPar | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter custo parceiro |
| accstafunn.prw | fVldEleg | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Validar elegível |
| accstafunn.prw | PFGetComVal | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | PF obter valor comissão |
| accstafunn.prw | RECTPCON | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo CON |
| accstafunn.prw | QtdeParc | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Quantidade parcelas |
| accstafunn.prw | VLDEPARA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Validar de/para |
| accstafunn.prw | fDeParaDO2 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | De/Para DO2 |

## Observações
- A pasta Comissões contém um sistema complexo de cálculo de comissões e remuneração variável
- Muitas funções não possuem informações de versão específica nos comentários
- A subpasta RV (Remuneração Variável) contém as funções mais recentes com versões específicas
- As classes estão organizadas na subpasta Environment Definition/Classes
- Existem integrações com sistemas externos como PSA e ACTIO
- O sistema possui funcionalidades de importação, relatórios, webservices e rotinas de ajuste
- Total estimado de mais de 600 User Functions distribuídas em múltiplas subpastas
- Sistema modular com separação clara de responsabilidades por subpasta

Resumo do que foi encontrado:
Estatísticas:

Total de User Functions: 600+ funções
Total de Classes: 22 classes
Estrutura modular com subpastas especializadas
Principais subpastas analisadas:

Environment Definition - Classes e funções base do sistema
Interfaces - Interfaces de usuário e funções de tela
RV - Remuneração Variável (sistema mais recente)
Relatorios - Relatórios diversos
Rotinas Ajustes - Rotinas de correção e ajuste
Webservices - Serviços web
Procedures - Procedures de banco
Funcoes Genericas - Funções utilitárias
Características principais:

Sistema de Comissões complexo com múltiplas regras de negócio
Remuneração Variável com integração PSA/ACTIO
Classes organizadas para diferentes entidades (Cliente, Produto, Título, etc.)
Versões específicas principalmente na subpasta RV (12.23.10, 12.1.33, etc.)
Integrações externas com sistemas PSA e ACTIO
Funcionalidades completas de importação, relatórios, webservices