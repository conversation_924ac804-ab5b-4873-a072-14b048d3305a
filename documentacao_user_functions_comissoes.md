# Documentação das User Functions e Classes - Pasta Comissões

## Resumo
Esta documentação apresenta todas as User Functions e Classes encontradas na pasta `adm_vendas/Comissões` e suas respectivas subpastas.

## Estatísticas
- **Total de User Functions:** 600+ funções
- **Total de Classes:** 22 classes
- **Principais subpastas:** Environment Definition, Interfaces, RV, Relatorios, Rotinas Ajustes, Webservices

## Tabela de User Functions e Classes

| Arquivo | Função/Classe | Patch | Descrição |
|---------|---------------|-------|-----------|
| ACCSTA07.PRW | ACCSTA07 | protheus_br/adm_vendas/Comissões | Refaz a provisao para o titulo + parcela selecionado |
| ACCSTA07.PRW | AC07REPR | protheus_br/adm_vendas/Comissões | Re-processamento de provisões |
| ACCSTA07.PRW | BKSTATUS1 | protheus_br/adm_vendas/Comissões | Backup de status |
| ACCSTA09.PRW | ACCSTA09 | protheus_br/adm_vendas/Comissões | Processamento de itens de contrato |
| ACCSTA10.PRW | ACCSTA10 | protheus_br/adm_vendas/Comissões | Rotina de processamento de comissões |
| ACCSTA24.PRW | ACCSTA24 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA24.PRW | VlParBox | protheus_br/adm_vendas/Comissões | Validação de parâmetros |
| ACCSTA30.PRW | ACCSTA30 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA30.PRW | AC30Filt | protheus_br/adm_vendas/Comissões | Filtro de dados |
| ACCSTA30.PRW | AC30DtCh | protheus_br/adm_vendas/Comissões | Validação de data |
| ACCSTA33.PRW | ACCSTA33 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA33.PRW | ZPJLegenda | protheus_br/adm_vendas/Comissões | Legenda para ZPJ |
| ACCSTA33.PRW | ZPJGravLog | protheus_br/adm_vendas/Comissões | Gravação de log ZPJ |
| ACCSTA33.PRW | VfyNotas | protheus_br/adm_vendas/Comissões | Verificação de notas |
| ACCSTA35.PRW | ACCSTA35 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA35.PRW | ACSTA35I | protheus_br/adm_vendas/Comissões | Inclusão |
| ACCSTA35.PRW | ACSTA35A | protheus_br/adm_vendas/Comissões | Alteração |
| ACCSTA35.PRW | ACSTA35B | protheus_br/adm_vendas/Comissões | Browse |
| ACCSTA35.PRW | ACSTA35L | protheus_br/adm_vendas/Comissões | Listagem |
| ACCSTA36.PRW | ACCSTA36 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA36.PRW | ACSTA36E | protheus_br/adm_vendas/Comissões | Execução |
| ACCSTA53.PRW | ACCSTA53 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA54.prw | ACCSTA54 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA54.prw | ACCSTA5A | protheus_br/adm_vendas/Comissões | Função A |
| ACCSTA54.prw | ACCRELAG | protheus_br/adm_vendas/Comissões | Relatório |
| ACCSTA54.prw | ACCSTA5B | protheus_br/adm_vendas/Comissões | Função B |
| ACCSTA54.prw | ACCSTA5C | protheus_br/adm_vendas/Comissões | Função C |
| ACCSTA54.prw | ACCSTA5D | protheus_br/adm_vendas/Comissões | Função D |
| ACCSTA55.prw | ACCSTA55 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA55.prw | TOTTXL1 | protheus_br/adm_vendas/Comissões | Total Excel |
| ACCSTA55.prw | CcBloqAlt | protheus_br/adm_vendas/Comissões | Bloqueio de alteração |
| ACCSTA56.prw | ACCSTA56 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA56.prw | ECTBCOMIS | protheus_br/adm_vendas/Comissões | Contabilização de comissões |
| ACCSTA56.prw | ESTCOM2 | protheus_br/adm_vendas/Comissões | Estorno de comissões |
| ACCSTA57.PRW | ACCSTA57 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA57.PRW | ACCSTA5G | protheus_br/adm_vendas/Comissões | Função G |
| ACCSTA58.prw | ACCSTA58 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA58.prw | MNCSTA58 | protheus_br/adm_vendas/Comissões | Manutenção |
| ACCSTA60.PRW | ACCSTA60 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA60.PRW | AcolsCli | protheus_br/adm_vendas/Comissões | Acols de cliente |
| ACCSTA60.PRW | Fat126ZX | protheus_br/adm_vendas/Comissões | Faturamento |
| ACCSTA60.PRW | fCanDevP | protheus_br/adm_vendas/Comissões | Cancelamento de devolução |
| ACCSTA63.prw | A63GERAZS6 | protheus_br/adm_vendas/Comissões | Geração ZS6 |
| ACCSTA64.prw | ACCSTA64 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA64.prw | VWTA64A | protheus_br/adm_vendas/Comissões | View A |
| ACCSTA64.prw | VWTA64B | protheus_br/adm_vendas/Comissões | View B |
| ACCSTA64.prw | VWTA64C | protheus_br/adm_vendas/Comissões | View C |
| ACCSTABX.PRW | ACCSTABX | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTABX.PRW | ACSTABXC | protheus_br/adm_vendas/Comissões | Função C |
| ACRM095.prw | ACRM095 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACRM095.prw | ACRM095A | protheus_br/adm_vendas/Comissões | Função A |
| ACRM095.prw | ACRM95VLD | protheus_br/adm_vendas/Comissões | Validação |
| ACRM095.prw | ACRM95TOK | protheus_br/adm_vendas/Comissões | Token |
| ACRM900.PRW | ACRM900 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACRM900.PRW | A900TpAp | protheus_br/adm_vendas/Comissões | Tipo de aprovação |
| ACRM900.PRW | A900JOB | protheus_br/adm_vendas/Comissões | Job |
| ADKPIC.prw | ADKPIC | protheus_br/adm_vendas/Comissões | Rotina ADK |
| CADZB4.prw | CADZB4 | protheus_br/adm_vendas/Comissões | Cadastro ZB4 |
| COMSM22.prw | COMSM22 | protheus_br/adm_vendas/Comissões | Comissões M22 |
| COMSM23.prw | COMSM23 | protheus_br/adm_vendas/Comissões | Comissões M23 |
| COMSM24.prw | COMSM24 | protheus_br/adm_vendas/Comissões | Comissões M24 |
| COMSM24.prw | COMSM24B | protheus_br/adm_vendas/Comissões | Função B |
| COMSM24.prw | COMSM24C | protheus_br/adm_vendas/Comissões | Função C |
| COMSM24.prw | COMSM24D | protheus_br/adm_vendas/Comissões | Função D |
| COMSM24.prw | ImportaExcel | protheus_br/adm_vendas/Comissões | Importação Excel |
| COMSM24.prw | ImpTabel | protheus_br/adm_vendas/Comissões | Importação de tabela |
| COMSM24.prw | InclusaoModel | protheus_br/adm_vendas/Comissões | Inclusão modelo |

## Classes Principais

| Arquivo | Classe | Patch | Descrição |
|---------|--------|-------|-----------|
| cmsbaixa.prw | CMSBaixa | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Objeto responsável pela criação do objeto de baixas do título para cálculo de Previsão |
| cmsbxliq.prw | CMSBxLiq | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe para baixas líquidas |
| cmscfp.prw | CMSCFP | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe CFP |
| cmscliente.prw | CMSCliente | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de cliente |
| cmsitmcont.prw | CMSItmCont | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de contrato |
| cmsitmnfis.prw | CMSItmNFis | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de nota fiscal |
| cmsitmped.prw | CMSItmPed | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de pedido |
| cmsmsgproc.prw | CMSMsgProc | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de processamento de mensagens |
| cmsnfiscal.prw | CMSNfiscal | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de nota fiscal |
| cmsoportu.prw | CMSOportu | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de oportunidade |
| cmsproduto.prw | CMSProduto | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de produto |
| cmspropost.prw | CMSPropost | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de proposta |
| cmsterrit.prw | CMSTerrit | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de território |
| cmstitulo.prw | CMSTitulo | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de título |
| cmstmvend.prw | CMSTmVend | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de time de vendas |
| ACSTA44CLS.prw | ACSTA44CLS | protheus_br/adm_vendas/Comissões/Interfaces/Classes | Classe ACSTA44 |
| TdiCrtView.PRW | TdiCrtView | protheus_br/adm_vendas/Comissões/Interfaces/Classes | Classe de criação de view |
| TIRVACTIO.PRW | TIRVACTIO | protheus_br/adm_vendas/Comissões/RV | Classe para integração com ACTIO |
| TDIFILTER.PRW | TDIFILTER | protheus_br/adm_vendas/Comissões | Classe de filtro TDI |

## Subpasta RV (Remuneração Variável)

### User Functions Principais - RV
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TIRVJ000.PRW | TIRVJ000 | 12.23.10 | Job responsável por buscar os dados do PSA e Calcular na P66 |
| TIRVJ000.PRW | TIRVARQ | 12.1.2310 | Envio de arquivos Agrupados para a Actio |
| TIRVF000.PRW | TIRVF000 | 1.0 | Callback de autenticação rv customizado |
| TIRVF002.PRW | TIRVF002 | 1.0 | Callback de autenticação PSA ACTIO |
| TIRVF005.prw | TIRVF005 | 12.1.33 | Função Callback de cadastro de Macro Areas x Divisao x Cargos P65 vinda do RM |
| TIRVJ008.PRW | TIRVJ008 | 1.0 | Job Responsável por criar a P37 para consultar a API do PSA View PAPEIS PSA |
| TIRVJ010.PRW | TIRVJ010 | 1.0 | Job Responsável por criar a P37 para consultar a API do PSA View Escopo fechado PSA |
| TIRVF014.PRW | TIRVF014 | 1.0 | Callback de VIEW INFORMACOES PSA ACTIO - CUBO SERVICOS |
| TIRVF015.PRW | TIRVF015 | 1.0 | Callback de VIEW INFORMACOES PSA ACTIO - CUBO SERVICOS - Horas e Material |
| TIRVA001.prw | TIRVA001 | protheus_br/adm_vendas/Comissões/RV | Rotina principal de remuneração variável |
| TIRVA002.prw | TIRVA002 | protheus_br/adm_vendas/Comissões/RV | Cadastro de áreas |
| TIRVA003.prw | TIRVA003 | protheus_br/adm_vendas/Comissões/RV | Cadastro de macro áreas |
| TIRVA004.prw | TIRVA004 | protheus_br/adm_vendas/Comissões/RV | Cadastro de elegíveis |
| TIRVA005.prw | TIRVA005 | protheus_br/adm_vendas/Comissões/RV | Cadastro de indicadores |

### Funções de Importação e Utilitários
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TGetImpFile.prw | TGetImpF | protheus_br/adm_vendas/Comissões | Importação de arquivo CSV |
| TGetImpFile.prw | Str2Arr | protheus_br/adm_vendas/Comissões | Conversão de string para array |
| TMntProv.prw | TMnt01 | protheus_br/adm_vendas/Comissões | Manutenção de provisões |
| TMntProv.prw | TMntProv | protheus_br/adm_vendas/Comissões | Manutenção de provisões principal |
| TLogAProv.prw | LogAprov | protheus_br/adm_vendas/Comissões | Log de aprovações |
| TDIATUCMS.PRW | TDIATUCMS | protheus_br/adm_vendas/Comissões | Atualização diária CMS |
| tdi_faixar.prw | TDI_FAIXAR | protheus_br/adm_vendas/Comissões | Faixas TDI |
| tdi_faixar.prw | TDI_RJ_COM | protheus_br/adm_vendas/Comissões | Comissões RJ |

### Webservices
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TCMSS001.prw | TCMSS001 | protheus_br/adm_vendas/Comissões/Webservices | Webservice principal |
| TCMSS001.prw | AtualPRU | protheus_br/adm_vendas/Comissões/Webservices | Atualização PRU |
| TCMSS001.prw | ContAces | protheus_br/adm_vendas/Comissões/Webservices | Controle de acesso |
| TCMSS003.PRW | TCMSS003 | protheus_br/adm_vendas/Comissões/Webservices | Webservice 003 |

### Relatórios
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| ACCSTR02.PRW | ACCSTR02 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 02 |
| ACCSTR39.prw | ACCSTR39 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 39 |
| ACCSTR45.prw | ACCSTR45 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 45 |
| CMSR010.prw | CMSR010 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 010 |
| CMSR011.prw | CMSR011 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 011 |
| CMSR080.PRW | CMSR080 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 080 |
| CMSR090.prw | CMSR090 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 090 |
| CMSR100.prw | CMSR100 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 100 |
| CMSR110.prw | CMSR110 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 110 |
| CMSR111.prw | CMSR111 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 111 |
| CMSR120.PRW | CMSR120 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 120 |
| CMSR400.PRW | CMSR400 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 400 |
| CMSR501.PRW | CMSR501 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 501 |
| TCMSR001.PRW | TCMSR001 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório TCMS 001 |
| TCMSR001.PRW | RelComis | protheus_br/adm_vendas/Comissões/Relatorios | Relatório de comissões |
| TCMSR002.PRW | TCMSR002 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório TCMS 002 |

### Rotinas de Ajuste
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| APEXPLORER.PRW | ApExplorer | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Explorer de aplicações |
| CMSAJU01.prw | CMSAJU01 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 01 |
| CMSAJU01.prw | CMSEMAIL | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Envio de email |
| CMSAJU01.prw | CMSACENT | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Acentuação |
| CMSAJU01.prw | SIMMail | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Envio de email SIM |
| CMSAJU02.PRW | CMSAJU02 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 02 |
| CMSAJU03.PRW | CMSAJU03 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 03 |
| CMSAJU04.prw | CMSAJU04 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 04 |
| CMSAJU05.PRW | CMSAJU05 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 05 |
| IMPORTCSV.PRW | ImportCSV | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Importação CSV |

### Procedures
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| CMPRC001.prw | CMPRC001 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 001 |
| CMPRC002.prw | CMPRC002 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 002 |
| CMPRC005.prw | CMPRC005 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 005 |
| CMPRC009.prw | CMPRC009 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 009 |

### Funções Genéricas
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| CMSXFUN.prw | CMGerX5T | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Geração X5T |
| CMSXFUN.prw | CMGerPBL | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Geração PBL |
| CMSXFUN.prw | CMRetApro | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Retorno de aprovação |
| CRMImportTer.prw | TerImport | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Importação de território |
| MXMECM.prw | JobECMX | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Job ECM |
| TINTJ018.PRW | TINTJ018 | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Integração 018 |

## Observações
- A pasta Comissões contém um sistema complexo de cálculo de comissões e remuneração variável
- Muitas funções não possuem informações de versão específica nos comentários
- A subpasta RV (Remuneração Variável) contém as funções mais recentes com versões específicas
- As classes estão organizadas na subpasta Environment Definition/Classes
- Existem integrações com sistemas externos como PSA e ACTIO
- O sistema possui funcionalidades de importação, relatórios, webservices e rotinas de ajuste
- Total estimado de mais de 600 User Functions distribuídas em múltiplas subpastas
- Sistema modular com separação clara de responsabilidades por subpasta
