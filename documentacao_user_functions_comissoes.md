# Documentação das User Functions e Classes - Pasta Comissões

## Resumo
Esta documentação apresenta todas as User Functions e Classes encontradas na pasta `adm_vendas/Comissões` e suas respectivas subpastas.

## Estatísticas
- **Total de User Functions:** 600+ funções
- **Total de Classes:** 22 classes
- **Principais subpastas:** Environment Definition, Interfaces, RV, Relatorios, Rotinas Ajustes, Webservices

## Tabela de User Functions e Classes

| Arquivo | Função/Classe | Patch | Descrição |
|---------|---------------|-------|-----------|
| ACCSTA07.PRW | ACCSTA07 | protheus_br/adm_vendas/Comissões | Refaz a provisao para o titulo + parcela selecionado |
| ACCSTA07.PRW | AC07REPR | protheus_br/adm_vendas/Comissões | Re-processamento de provisões |
| ACCSTA07.PRW | BKSTATUS1 | protheus_br/adm_vendas/Comissões | Backup de status |
| ACCSTA09.PRW | ACCSTA09 | protheus_br/adm_vendas/Comissões | Processamento de itens de contrato |
| ACCSTA10.PRW | ACCSTA10 | protheus_br/adm_vendas/Comissões | Rotina de processamento de comissões |
| ACCSTA24.PRW | ACCSTA24 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA24.PRW | VlParBox | protheus_br/adm_vendas/Comissões | Validação de parâmetros |
| ACCSTA30.PRW | ACCSTA30 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA30.PRW | AC30Filt | protheus_br/adm_vendas/Comissões | Filtro de dados |
| ACCSTA30.PRW | AC30DtCh | protheus_br/adm_vendas/Comissões | Validação de data |
| ACCSTA33.PRW | ACCSTA33 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA33.PRW | ZPJLegenda | protheus_br/adm_vendas/Comissões | Legenda para ZPJ |
| ACCSTA33.PRW | ZPJGravLog | protheus_br/adm_vendas/Comissões | Gravação de log ZPJ |
| ACCSTA33.PRW | VfyNotas | protheus_br/adm_vendas/Comissões | Verificação de notas |
| ACCSTA35.PRW | ACCSTA35 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA35.PRW | ACSTA35I | protheus_br/adm_vendas/Comissões | Inclusão |
| ACCSTA35.PRW | ACSTA35A | protheus_br/adm_vendas/Comissões | Alteração |
| ACCSTA35.PRW | ACSTA35B | protheus_br/adm_vendas/Comissões | Browse |
| ACCSTA35.PRW | ACSTA35L | protheus_br/adm_vendas/Comissões | Listagem |
| ACCSTA36.PRW | ACCSTA36 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA36.PRW | ACSTA36E | protheus_br/adm_vendas/Comissões | Execução |
| ACCSTA53.PRW | ACCSTA53 | protheus_br/adm_vendas/Comissões | Rotina de processamento |
| ACCSTA54.prw | ACCSTA54 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA54.prw | ACCSTA5A | protheus_br/adm_vendas/Comissões | Função A |
| ACCSTA54.prw | ACCRELAG | protheus_br/adm_vendas/Comissões | Relatório |
| ACCSTA54.prw | ACCSTA5B | protheus_br/adm_vendas/Comissões | Função B |
| ACCSTA54.prw | ACCSTA5C | protheus_br/adm_vendas/Comissões | Função C |
| ACCSTA54.prw | ACCSTA5D | protheus_br/adm_vendas/Comissões | Função D |
| ACCSTA55.prw | ACCSTA55 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA55.prw | TOTTXL1 | protheus_br/adm_vendas/Comissões | Total Excel |
| ACCSTA55.prw | CcBloqAlt | protheus_br/adm_vendas/Comissões | Bloqueio de alteração |
| ACCSTA56.prw | ACCSTA56 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA56.prw | ECTBCOMIS | protheus_br/adm_vendas/Comissões | Contabilização de comissões |
| ACCSTA56.prw | ESTCOM2 | protheus_br/adm_vendas/Comissões | Estorno de comissões |
| ACCSTA57.PRW | ACCSTA57 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA57.PRW | ACCSTA5G | protheus_br/adm_vendas/Comissões | Função G |
| ACCSTA58.prw | ACCSTA58 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA58.prw | MNCSTA58 | protheus_br/adm_vendas/Comissões | Manutenção |
| ACCSTA60.PRW | ACCSTA60 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA60.PRW | AcolsCli | protheus_br/adm_vendas/Comissões | Acols de cliente |
| ACCSTA60.PRW | Fat126ZX | protheus_br/adm_vendas/Comissões | Faturamento |
| ACCSTA60.PRW | fCanDevP | protheus_br/adm_vendas/Comissões | Cancelamento de devolução |
| ACCSTA63.prw | A63GERAZS6 | protheus_br/adm_vendas/Comissões | Geração ZS6 |
| ACCSTA64.prw | ACCSTA64 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTA64.prw | VWTA64A | protheus_br/adm_vendas/Comissões | View A |
| ACCSTA64.prw | VWTA64B | protheus_br/adm_vendas/Comissões | View B |
| ACCSTA64.prw | VWTA64C | protheus_br/adm_vendas/Comissões | View C |
| ACCSTABX.PRW | ACCSTABX | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACCSTABX.PRW | ACSTABXC | protheus_br/adm_vendas/Comissões | Função C |
| ACRM095.prw | ACRM095 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACRM095.prw | ACRM095A | protheus_br/adm_vendas/Comissões | Função A |
| ACRM095.prw | ACRM95VLD | protheus_br/adm_vendas/Comissões | Validação |
| ACRM095.prw | ACRM95TOK | protheus_br/adm_vendas/Comissões | Token |
| ACRM900.PRW | ACRM900 | protheus_br/adm_vendas/Comissões | Rotina principal |
| ACRM900.PRW | A900TpAp | protheus_br/adm_vendas/Comissões | Tipo de aprovação |
| ACRM900.PRW | A900JOB | protheus_br/adm_vendas/Comissões | Job |
| ADKPIC.prw | ADKPIC | protheus_br/adm_vendas/Comissões | Rotina ADK |
| CADZB4.prw | CADZB4 | protheus_br/adm_vendas/Comissões | Cadastro ZB4 |
| COMSM22.prw | COMSM22 | protheus_br/adm_vendas/Comissões | Comissões M22 |
| COMSM23.prw | COMSM23 | protheus_br/adm_vendas/Comissões | Comissões M23 |
| COMSM24.prw | COMSM24 | protheus_br/adm_vendas/Comissões | Comissões M24 |
| COMSM24.prw | COMSM24B | protheus_br/adm_vendas/Comissões | Função B |
| COMSM24.prw | COMSM24C | protheus_br/adm_vendas/Comissões | Função C |
| COMSM24.prw | COMSM24D | protheus_br/adm_vendas/Comissões | Função D |
| COMSM24.prw | ImportaExcel | protheus_br/adm_vendas/Comissões | Importação Excel |
| COMSM24.prw | ImpTabel | protheus_br/adm_vendas/Comissões | Importação de tabela |
| COMSM24.prw | InclusaoModel | protheus_br/adm_vendas/Comissões | Inclusão modelo |

## Classes Principais

| Arquivo | Classe | Patch | Descrição |
|---------|--------|-------|-----------|
| cmsbaixa.prw | CMSBaixa | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Objeto responsável pela criação do objeto de baixas do título para cálculo de Previsão |
| cmsbxliq.prw | CMSBxLiq | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe para baixas líquidas |
| cmscfp.prw | CMSCFP | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe CFP |
| cmscliente.prw | CMSCliente | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de cliente |
| cmsitmcont.prw | CMSItmCont | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de contrato |
| cmsitmnfis.prw | CMSItmNFis | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de nota fiscal |
| cmsitmped.prw | CMSItmPed | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de item de pedido |
| cmsmsgproc.prw | CMSMsgProc | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de processamento de mensagens |
| cmsnfiscal.prw | CMSNfiscal | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de nota fiscal |
| cmsoportu.prw | CMSOportu | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de oportunidade |
| cmsproduto.prw | CMSProduto | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de produto |
| cmspropost.prw | CMSPropost | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de proposta |
| cmsterrit.prw | CMSTerrit | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de território |
| cmstitulo.prw | CMSTitulo | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de título |
| cmstmvend.prw | CMSTmVend | protheus_br/adm_vendas/Comissões/Environment Definition/Classes | Classe de time de vendas |
| ACSTA44CLS.prw | ACSTA44CLS | protheus_br/adm_vendas/Comissões/Interfaces/Classes | Classe ACSTA44 |
| TdiCrtView.PRW | TdiCrtView | protheus_br/adm_vendas/Comissões/Interfaces/Classes | Classe de criação de view |
| TIRVACTIO.PRW | TIRVACTIO | protheus_br/adm_vendas/Comissões/RV | Classe para integração com ACTIO |
| TDIFILTER.PRW | TDIFILTER | protheus_br/adm_vendas/Comissões | Classe de filtro TDI |

## Subpasta RV (Remuneração Variável)

### User Functions Principais - RV
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TIRVJ000.PRW | TIRVJ000 | 12.23.10 | Job responsável por buscar os dados do PSA e Calcular na P66 |
| TIRVJ000.PRW | TIRVARQ | 12.1.2310 | Envio de arquivos Agrupados para a Actio |
| TIRVF000.PRW | TIRVF000 | 1.0 | Callback de autenticação rv customizado |
| TIRVF002.PRW | TIRVF002 | 1.0 | Callback de autenticação PSA ACTIO |
| TIRVF005.prw | TIRVF005 | 12.1.33 | Função Callback de cadastro de Macro Areas x Divisao x Cargos P65 vinda do RM |
| TIRVJ008.PRW | TIRVJ008 | 1.0 | Job Responsável por criar a P37 para consultar a API do PSA View PAPEIS PSA |
| TIRVJ010.PRW | TIRVJ010 | 1.0 | Job Responsável por criar a P37 para consultar a API do PSA View Escopo fechado PSA |
| TIRVF014.PRW | TIRVF014 | 1.0 | Callback de VIEW INFORMACOES PSA ACTIO - CUBO SERVICOS |
| TIRVF015.PRW | TIRVF015 | 1.0 | Callback de VIEW INFORMACOES PSA ACTIO - CUBO SERVICOS - Horas e Material |
| TIRVA001.prw | TIRVA001 | protheus_br/adm_vendas/Comissões/RV | Rotina principal de remuneração variável |
| TIRVA002.prw | TIRVA002 | protheus_br/adm_vendas/Comissões/RV | Cadastro de áreas |
| TIRVA003.prw | TIRVA003 | protheus_br/adm_vendas/Comissões/RV | Cadastro de macro áreas |
| TIRVA004.prw | TIRVA004 | protheus_br/adm_vendas/Comissões/RV | Cadastro de elegíveis |
| TIRVA005.prw | TIRVA005 | protheus_br/adm_vendas/Comissões/RV | Cadastro de indicadores |

### Funções de Importação e Utilitários
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TGetImpFile.prw | TGetImpF | protheus_br/adm_vendas/Comissões | Importação de arquivo CSV |
| TGetImpFile.prw | Str2Arr | protheus_br/adm_vendas/Comissões | Conversão de string para array |
| TMntProv.prw | TMnt01 | protheus_br/adm_vendas/Comissões | Manutenção de provisões |
| TMntProv.prw | TMntProv | protheus_br/adm_vendas/Comissões | Manutenção de provisões principal |
| TLogAProv.prw | LogAprov | protheus_br/adm_vendas/Comissões | Log de aprovações |
| TDIATUCMS.PRW | TDIATUCMS | protheus_br/adm_vendas/Comissões | Atualização diária CMS |
| tdi_faixar.prw | TDI_FAIXAR | protheus_br/adm_vendas/Comissões | Faixas TDI |
| tdi_faixar.prw | TDI_RJ_COM | protheus_br/adm_vendas/Comissões | Comissões RJ |

### Webservices
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TCMSS001.prw | TCMSS001 | protheus_br/adm_vendas/Comissões/Webservices | Webservice principal |
| TCMSS001.prw | AtualPRU | protheus_br/adm_vendas/Comissões/Webservices | Atualização PRU |
| TCMSS001.prw | ContAces | protheus_br/adm_vendas/Comissões/Webservices | Controle de acesso |
| TCMSS003.PRW | TCMSS003 | protheus_br/adm_vendas/Comissões/Webservices | Webservice 003 |

### Relatórios
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| ACCSTR02.PRW | ACCSTR02 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 02 |
| ACCSTR39.prw | ACCSTR39 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 39 |
| ACCSTR45.prw | ACCSTR45 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório 45 |
| CMSR010.prw | CMSR010 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 010 |
| CMSR011.prw | CMSR011 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 011 |
| CMSR080.PRW | CMSR080 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 080 |
| CMSR090.prw | CMSR090 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 090 |
| CMSR100.prw | CMSR100 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 100 |
| CMSR110.prw | CMSR110 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 110 |
| CMSR111.prw | CMSR111 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 111 |
| CMSR120.PRW | CMSR120 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 120 |
| CMSR400.PRW | CMSR400 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 400 |
| CMSR501.PRW | CMSR501 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório CMS 501 |
| TCMSR001.PRW | TCMSR001 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório TCMS 001 |
| TCMSR001.PRW | RelComis | protheus_br/adm_vendas/Comissões/Relatorios | Relatório de comissões |
| TCMSR002.PRW | TCMSR002 | protheus_br/adm_vendas/Comissões/Relatorios | Relatório TCMS 002 |

### Rotinas de Ajuste
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| APEXPLORER.PRW | ApExplorer | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Explorer de aplicações |
| CMSAJU01.prw | CMSAJU01 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 01 |
| CMSAJU01.prw | CMSEMAIL | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Envio de email |
| CMSAJU01.prw | CMSACENT | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Acentuação |
| CMSAJU01.prw | SIMMail | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Envio de email SIM |
| CMSAJU02.PRW | CMSAJU02 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 02 |
| CMSAJU03.PRW | CMSAJU03 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 03 |
| CMSAJU04.prw | CMSAJU04 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 04 |
| CMSAJU05.PRW | CMSAJU05 | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Ajuste 05 |
| IMPORTCSV.PRW | ImportCSV | protheus_br/adm_vendas/Comissões/Rotinas Ajustes | Importação CSV |

### Procedures
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| CMPRC001.prw | CMPRC001 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 001 |
| CMPRC002.prw | CMPRC002 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 002 |
| CMPRC005.prw | CMPRC005 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 005 |
| CMPRC009.prw | CMPRC009 | protheus_br/adm_vendas/Comissões/Procedures | Procedure 009 |

### Funções Genéricas
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| CMSXFUN.prw | CMGerX5T | P11 | Criar o índice H (INPC-TJSP) na tabela X3 no cadastro da SX5 |
| CMSXFUN.prw | CMGerPBL | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Geração PBL - Auto preenchimento para novas filiais |
| CMSXFUN.prw | CMRetApro | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Retorno de aprovação - Processo de aprovação |
| CMSXFUN.prw | ADYTPROP | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Validação para edição de campo tipo proposta |
| CMSXFUN.prw | GetLinRec | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Obter linha de receita do produto |
| CMSXFUN.prw | FGetSupN | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Obter supervisor por unidade de vendas |
| CMSXFUN.prw | FUndVinc | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Obter unidades vinculadas |
| CMSXFUN.prw | GetCmsEmp | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Obter empresas CMS |
| CMSXFUN.prw | BuscaGc | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Buscar grupo de comissão do produto |
| CRMImportTer.prw | TerImport | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Rotina de importação de territórios |
| MXMECM.prw | JobECMX | protheus_br/adm_vendas/Comissões/Funcoes Genericas | Job de integração Maxime com ECM |
| TINTJ018.PRW | TINTJ018 | 1.0 | Rotina responsável por reenviar os JSON no qual o envio disparado no commit do ponto de entrada falhou |

### Interfaces Functions - Principais Rotinas
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| ACCSTA02.PRW | ACCSTA02 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Refaz a provisão para o Documento + Parcela posicionado |
| ACCSTA02.PRW | ACSTA02G | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração de provisão |
| ACCSTA03.PRW | ACCSTA03 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina principal de processamento |
| ACCSTA03.PRW | fGeraZPX | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração de ZPX |
| ACCSTA03.PRW | MA03REPROC | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Reprocessamento |
| ACCSTA03.PRW | ACCST03B | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Função B |
| ACCSTA03.PRW | ACCSTJ03 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Job 03 |
| ACCSTA03L.PRW | ACSTA03L | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Versão L da rotina 03 |
| ACCSTA03L.PRW | fxGeraZPX | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração ZPX estendida |
| ACCSTA03L.PRW | XMA03REPROC | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Reprocessamento estendido |
| ACCSTA04.PRW | ACCSTA04 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 04 |
| ACCSTA04.PRW | FGETAGN0 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter agente |
| ACCSTA06.PRW | ACCSTA06 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 06 |
| ACCSTA11.PRW | ACCSTA11 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 11 |
| ACCSTA11.PRW | ACRE11LEGE | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Legenda 11 |
| ACCSTA11.PRW | ACRE11CONS | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Consulta 11 |
| ACCSTA11.PRW | fGetStat | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter status |
| ACCSTA11.PRW | ACRE11PR | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processamento 11 |
| ACCSTA11.PRW | PREPLERDA | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Preparar leitura |
| ACCSTA11.PRW | GETNOMEAR | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter nome AR |
| ACCSTA11.PRW | PREPAMB | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Preparar ambiente |
| ACCSTA11.PRW | GETPROP | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter proposta |
| ACCSTA11.PRW | fRetFranq | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Retornar franquia |
| ACCSTA11.PRW | RUNSTA02 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Executar STA02 |
| ACCSTA11.PRW | TdiForm | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Formulário TDI |
| ACCSTA11.PRW | GETSTORE | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter loja |
| ACCSTA14.PRW | ACCSTA14 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 14 com parâmetros |
| ACCSTA14.PRW | ACSTA14P | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processamento 14 |
| ACCSTA22.prw | ACCSTA22 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 22 |
| ACCSTA22.prw | ACCVISU | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Visualização |
| ACCSTA22.prw | ACC22LinOK | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validação de linha |
| ACCSTA22.prw | ACC22TudoOK | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validação geral |
| ACCSTA22.prw | XModelo3 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Modelo 3 customizado |
| ACCSTA25.PRW | ACCSTA25 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 25 |
| ACCSTA26.prw | ACCSTA26 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 26 |
| ACCSTA31.prw | ACCSTA31 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 31 com filtro |
| ACCSTA31.prw | ReplaceGC | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Substituir grupo comissão |
| ACCSTA31.prw | ZD2IDREC | protheus_br/adm_vendas/Comissões/Interfaces/Functions | ID registro ZD2 |
| ACCSTA31.prw | ZD2Util | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Utilitários ZD2 |
| ACCSTA32.PRW | ACCSTA32 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 32 |
| ACCSTA37.PRW | ACCSTA37 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 37 |
| ACCSTA37.PRW | ACSTA37C | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Função C da rotina 37 |
| ACCSTA38.prw | ACCSTA38 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 38 |
| ACCSTA39.PRW | ACCSTA39 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 39 |
| ACCSTA39.PRW | TDI_SWREG | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Switch de regiões TDI |
| ACCSTA40.PRW | ACCSTA40 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 40 |
| ACCSTA40.PRW | AC40Filt | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Filtro 40 |
| ACCSTA41.prw | ACCSTA41 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 41 |
| ACCSTA42.PRW | ACCSTA42 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 42 |
| ACCSTA42.PRW | ACSTA42P | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processamento 42 |
| ACCSTA43.PRW | ACCSTA43 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 43 |

### Interfaces Functions - Cadastros e Manutenções (ACCSTA44-99)
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| ACCSTA44.prw | ACCSTA44 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 44 |
| ACCSTA44.prw | ACS44CAD | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Cadastro 44 |
| ACCSTA44.prw | ACS44ATU | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Atualização 44 |
| ACCSTA44.prw | ACS44PRE | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Preparação 44 |
| ACCSTA44.prw | ACSTA44P | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processamento 44 |
| ACCSTA44.prw | ACS44RAT | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rateio 44 |
| ACCSTA44.prw | U_ACSRATOK | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validação rateio |
| ACCSTA45.prw | ACCSTA45 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 45 com proposta |
| ACCSTA46.prw | ACCSTA46 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 46 |
| ACCSTA47.prw | ACCSTA47 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 47 |
| ACCSTA47.prw | ACS47EXP | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Exportação 47 |
| ACCSTA48.PRW | ACSTA48P | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processamento 48 |
| ACCSTA49.prw | ACCSTA49 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 49 |
| ACCSTA49.prw | ACSTA49P | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processamento 49 |
| ACCSTA50.PRW | ACCSTA50 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 50 |
| ACCSTA51.prw | ACCSTA51 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 51 |
| ACCSTA51.prw | CCS51MNT | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 51 |
| ACCSTA51.prw | AC51PreV | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Pré-validação 51 |
| ACCSTA51.prw | AC51Bx | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Baixa 51 |
| ACCSTA51.prw | AC51Est | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Estorno 51 |
| ACCSTA51.prw | CCS51VLD | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validação 51 |
| ACCSTA51.prw | CCS51GT1 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Get 1 da rotina 51 |
| ACCSTA51.prw | CCS51CS1 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CS1 da rotina 51 |
| ACCSTA51.prw | GetARNome | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter nome AR |
| ACCSTA51_PE.prw | ACSTA51 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Ponto de entrada 51 |
| ACCSTA52.prw | ACCSTA52 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 52 |
| ACCSTA52.prw | A52Manut | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 52 |
| ACCSTA52.prw | AC52VlEx | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validação exclusão 52 |
| ACCSTA61.prw | ACCSTA61 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 61 |
| ACCSTA61.prw | ACSTA61A | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Função A da rotina 61 |
| ACCSTA61.prw | ACSTA61B | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Função B da rotina 61 |
| ACCSTA61.prw | ACSTA61C | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Função C da rotina 61 |
| ACCSTA61.prw | XCONTRLOG | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Controle de log |
| ACCSTA65.prw | ACCSTA65 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 65 |
| ACCSTA65.prw | ACST65TP | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Tipo 65 |
| ACCSTA65.prw | ACST65CO | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Consulta 65 |
| ACCSTA67.prw | ACCSTA67 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 67 com automação |
| ACCSTA67.prw | PNLEGENG | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Legenda engenharia |
| ACCSTA68.prw | ACCSTA68 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 68 |
| ACCSTA69.prw | ACCSTA69 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 69 |
| ACCSTA6A.prw | ACCSTA6A | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 6A |
| ACCSTA6B.prw | ACCSTA6B | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 6B com parâmetros |
| ACCSTA6C.prw | ACCSTA6C | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 6C |
| ACCSTA70.prw | ACCSTA70 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 70 |
| ACCSTA70.prw | ACCST70Atu | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Atualização 70 |
| ACCSTA71.prw | ACCSTA71 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 71 |
| ACCSTA71.prw | fImpCSV | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Importação CSV |
| ACCSTA72.prw | ACCSTA72 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 72 |
| ACCSTA73.prw | ACCSTA73 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 73 |
| ACCSTA73.prw | ACSTA73 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Função alternativa 73 |
| ACCSTA73.prw | ACSTA7A | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Função 7A |
| ACCSTA80.prw | ACCSTA80 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 80 |
| ACCSTA80.prw | ACSTA80ALT | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Alteração 80 |
| ACCSTA90.prw | ACCSTA90 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 90 |
| ACCSTA90_PE.prw | ACSTA90P | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Ponto de entrada 90 |
| ACCSTA91.prw | ACCSTA91 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 91 com filtro |
| ACCSTA91.prw | CnGpCom | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Consulta grupo comissão |
| ACCSTA91.prw | CnGrpRt | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Consulta grupo rateio |
| ACCSTA92.prw | ACCSTA92 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 92 |
| ACCSTA93.prw | ACCSTA93 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 93 |
| ACCSTA99.PRW | ACCSTA99 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Rotina 99 |

### Interfaces Functions - Manutenções (ACCSTM01-54)
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| ACCSTM01.prw | ACCSTM01 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 01 |
| ACCSTM01.prw | TdiGetCep | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter CEP TDI |
| ACCSTM02.PRW | ACCSTM02 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 02 |
| ACCSTM02.PRW | CstGetPc | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter percentual custo |
| ACCSTM02.PRW | fValidCNPJ | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validar CNPJ |
| ACCSTM03.PRW | ACCSTM03 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 03 |
| ACCSTM04.PRW | ACCSTM04 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 04 |
| ACCSTM04.PRW | ACSTM04A | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Alteração 04 |
| ACCSTM04.PRW | ACSTM04I | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Inclusão 04 |
| ACCSTM04.PRW | ACSTM04D | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Exclusão 04 |
| ACCSTM04.PRW | ACSTM04O | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Operação 04 |
| ACCSTM05.PRW | ACCSTM05 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 05 |
| ACCSTM05.PRW | CmsGrpMt | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Grupo meta CMS |
| ACCSTM05.PRW | CmsM5GeD | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração dados M5 |
| ACCSTM05.PRW | CmsGetPi | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter picture CMS |
| ACCSTM06.PRW | ACCSTM06 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 06 |
| ACCSTM07.PRW | ACCSTM07 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 07 |
| ACCSTM08.PRW | ACCSTM08 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 08 |
| ACCSTM09.PRW | ACCSTM09 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 09 |
| ACCSTM09.PRW | VlDtCMS | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validar data CMS |
| ACCSTM10.prw | ACCSTM10 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 10 |
| ACCSTM10.prw | FGETAGNM | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Obter agente nome |
| ACCSTM11.prw | ACCSTM11 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 11 |
| ACCSTM12.prw | ACCSTM12 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 12 |
| ACCSTM14.PRW | ACCSTM14 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 14 |
| ACCSTM15.PRW | ACCSTM15 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 15 |
| ACCSTM16.PRW | ACCSTM16 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 16 |
| ACCSTM17.PRW | ACCSTM17 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 17 com filtro |
| ACCSTM18.PRW | ACCSTM18 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 18 |
| ACCSTM18.PRW | M18RETAPV | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Retorno aprovação M18 |
| ACCSTM19.PRW | ACCSTM19 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 19 |
| ACCSTM19.PRW | PROREA | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processo reativação |
| ACCSTM19.PRW | PROCREA | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processo criação |
| ACCSTM19.PRW | PROCPRO | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processo produção |
| ACCSTM19.PRW | TstBrow | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Teste browse |
| ACCSTM19.PRW | Marcar | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Marcar registros |
| ACCSTM19.PRW | DesMar | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Desmarcar registros |
| ACCSTM19.PRW | Mark | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Marcar |
| ACCSTM19.PRW | TMarkAll | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Marcar todos |
| ACCSTM19.PRW | GERAPR | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Gerar aprovação |
| ACCSTM19.PRW | GravaLog | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Gravar log |
| ACCSTM34.prw | ACCSTM34 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 34 |
| ACCSTM34.prw | MsgShow | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Mostrar mensagem |
| ACCSTM35.prw | ACCSTM35 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 35 |
| ACCSTM36.prw | ACCSTM36 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 36 |
| ACCSTM37.prw | ACCSTM37 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 37 |
| ACCSTM37.prw | STM37STJ | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Status job M37 |
| ACCSTM38.prw | ACCSTM38 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 38 |
| ACCSTM39.prw | ACCSTM39 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 39 |
| ACCSTM40.prw | ACCSTM40 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 40 |
| ACCSTM41.prw | ACCSTM41 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 41 com objeto |
| ACCSTM41.prw | GACSTM41 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração M41 |
| ACCSTM41.prw | ACCSVlCmp | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validar campo ACC |
| ACCSTM42.PRW | ACCSTM42 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 42 com objeto |
| ACCSTM42.PRW | GACSTM42 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração M42 |
| ACCSTM42.PRW | Extrair | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Extrair tabela |
| ACCSTM43.PRW | ACCSTM43 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 43 com objeto |
| ACCSTM43.PRW | Stm43PAp | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processamento aplicação M43 |
| ACCSTM43.PRW | GACSTM43 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração M43 |
| ACCSTM44.PRW | ACCSTM44 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 44 com objeto |
| ACCSTM44.PRW | Stm44PAp | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Processamento aplicação M44 |
| ACCSTM44.PRW | GACSTM44 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração M44 |
| ACCSTM45.prw | ACCSTM45 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 45 com campanha |
| ACCSTM45.prw | VerBaixas | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Verificar baixas |
| ACCSTM46.prw | ACCSTM46 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 46 |
| ACCSTM47.prw | ACCSTM47 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 47 |
| ACCSTM48.PRW | ACCSTM48 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 48 |
| ACCSTM49.prw | ACCSTM49 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 49 com objeto |
| ACCSTM49.prw | SELOPC | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Selecionar opção |
| ACCSTM49.prw | GACSTM49 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração M49 |
| ACCSTM49.prw | GECSTM49 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração estorno M49 |
| ACCSTM49.prw | VLDOPCAO | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validar opção |
| ACCSTM49.prw | GDCSTM49 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Grid M49 |
| ACCSTM50.prw | ACCSTM50 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 50 |
| ACCSTM50.prw | ACSTM50 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Operação M50 |
| ACCSTM51.prw | ACCSTM51 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 51 |
| ACCSTM51.prw | ACST51 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Função 51 |
| ACCSTM51.prw | ACS51EXC | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Exclusão 51 |
| ACCSTM51.prw | ACST51U | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Atualização 51 |
| ACCSTM52.prw | ACCSTM52 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 52 |
| ACCSTM53.PRW | ACCSTM53 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 53 |
| ACCSTM54.prw | ACCSTM54 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Manutenção 54 com objeto |
| ACCSTM54.prw | SELOPCMO | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Selecionar opção modelo |
| ACCSTM54.prw | GACSTM54 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Geração M54 |
| ACCSTM54.prw | GDCSTM54 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Grid M54 |

### Interfaces Functions - CMS e Relatórios
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| ACCSTR05.PRW | ACCSTR05 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Relatório STR05 |
| ACCSTR46.prw | ACCSTR46 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Relatório STR46 com log |
| CMINT001.prw | CMINT001 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Integração CMS 001 |
| CMINT001.prw | TArRspCom | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Array resposta comissão |
| CMSA001.prw | CMSA001 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS A001 |
| CMSA0100.PRW | CMSA0100 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS A0100 |
| CMSA0100.PRW | CMSA1Leg | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Legenda A1 |
| CMSA0110.prw | CMSA0110 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS A0110 |
| CMSA0110.prw | CMSALeg | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Legenda CMS A |
| CMSA0110.prw | FINAVIZPX | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Finalizar aviso ZPX |
| CMSA0120.prw | CMSA0120 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS A0120 |
| CMSA0130.prw | CMS0130 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS 0130 |
| CMSA0130.prw | CM0130Le | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Legenda 0130 |
| CMSA0130.prw | FINAVIS1 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Finalizar aviso 1 |
| CMSA0140.prw | CMSA0140 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS A0140 |
| CMSA0140.prw | CM140Leg | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Legenda 140 |
| CMSA110.prw | CMSA110 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS A110 |
| CMSA110.prw | CA110Wiz | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Wizard A110 |
| CMSA110.prw | CA110Det | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Detalhes A110 |
| CMSA110.prw | CA110Leg | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Legenda A110 |
| CMSA110.prw | CA110Pesq | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Pesquisa A110 |
| CMSA110.prw | CA110Apv | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Aprovação A110 |
| CMSA110A.prw | CMSA110A | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS A110A com vendedor |
| CMSA110A.prw | CA110AEx | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Exportação A110A |
| CMSA110A.prw | CA110ALe | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Legenda A110A |
| CMSA110A.prw | CA110ARep | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Reprocessamento A110A |
| CMSA110A.prw | CA110AUR | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Atualização A110A |
| CMSA120.prw | CMSA120 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS A120 |
| CMSABLOQ.prw | CMSABLOQ | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS bloqueio |
| CMSABLOQ.prw | CMSABLQ | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS aplicar bloqueio |
| CMSABLOQ.prw | CMSVVEND | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS validar vendedor |
| CMSADETAILS.prw | CMSADETA | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS detalhes com filtro |
| CMSC001.prw | CMSC001 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS C001 |
| CMSREL01.PRW | CMSREL01 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS Relatório 01 |
| CMSREL01.PRW | CMS01LOK | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Line OK 01 |
| CMSREL01.PRW | CMS01VExc | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validar exclusão 01 |
| CMSREL01.PRW | CMS01TOK | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Tudo OK 01 |
| CMSREL01.PRW | CMS01ZS2 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | ZS2 relatório 01 |
| CMSREL01.PRW | CMS01ZS5 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | ZS5 relatório 01 |
| CMSREL02.PRW | CMSREL02 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS Relatório 02 |
| CMSREL02.PRW | CMS02VExc | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Validar exclusão 02 |
| CMSREL02.PRW | CMS02LOK | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Line OK 02 |
| CMSREL02.PRW | CMS02TOK | protheus_br/adm_vendas/Comissões/Interfaces/Functions | Tudo OK 02 |
| CMSREL04.PRW | CMSREL04 | protheus_br/adm_vendas/Comissões/Interfaces/Functions | CMS Relatório 04 |

### Environment Definition Functions
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| accsta01.prw | ACCSTA01 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Cálculo de provisão de Comissão |
| accsta13.prw | ACCSTA13 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Agendamento da provisão de comissão |
| accsta13.prw | ACSTA13C | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Processamento por thread |
| accsta13.prw | ValDupAg | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Validação de duplicação de agendamento |
| accsta13.prw | AtuZPJ | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Atualização ZPJ |
| accsta13.prw | VerAgDat | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificação de agendamento por data |
| accsta13.prw | ACC13LgErr | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Log de erro |
| accsta13.prw | ACSTA13D | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Processamento distribuído |
| accsta13.prw | STJOBCMS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Status job CMS |
| accsta13.prw | QuebraNf | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Quebra de nota fiscal |
| accsta13.prw | CALCDIAR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Cálculo diário |
| accsta13.prw | DELZCC | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Exclusão ZCC |
| accstxfcmp.prw | ExecModApu | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Executa Modelo de Apuração da Campanha |
| accstxfcmp.prw | ExecArcor | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Execução Arcor |

### Funções Auxiliares (accstafunn.prw)
| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| accstafunn.prw | C01ATRASO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Cálculo de atraso |
| accstafunn.prw | fContrato | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Função de contrato |
| accstafunn.prw | fChk180Pg | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificação 180 páginas |
| accstafunn.prw | fHaveCP | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificação contas a pagar |
| accstafunn.prw | fGetPTerri | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter território |
| accstafunn.prw | fTerFaixa | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Território por faixa |
| accstafunn.prw | fCdu2010 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | CDU 2010 |
| accstafunn.prw | fSMS2010 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | SMS 2010 |
| accstafunn.prw | FUNSCS2010 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | SCS 2010 |
| accstafunn.prw | FAIXAATR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Faixa de atraso |
| accstafunn.prw | fRetComi | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Retorno de comissão |
| accstafunn.prw | TMVByPrpst | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time de vendas por proposta |
| accstafunn.prw | TMVByCFP | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time de vendas por CFP |
| accstafunn.prw | TMVByCli | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time de vendas por cliente |
| accstafunn.prw | TMVByZAY | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time de vendas por ZAY |
| accstafunn.prw | TMVAddTerr | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar território ao time de vendas |
| accstafunn.prw | TMVAddParc | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar parceiro |
| accstafunn.prw | FANTEIMPOS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Antecipação de impostos |
| accstafunn.prw | FUNPCOMPAD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual comissão padrão |
| accstafunn.prw | GETPCOMZD1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter percentual comissão ZD1 |
| accstafunn.prw | FTIPCOM001 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão 001 |
| accstafunn.prw | RECTPCPAD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo comissão padrão |
| accstafunn.prw | RECTIPCCDU | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo comissão CDU |
| accstafunn.prw | TIPCOMP1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão P1 |
| accstafunn.prw | TIPCOMP2 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão P2 |
| accstafunn.prw | TIPCOMP3 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão P3 |
| accstafunn.prw | FISBLINE | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Linha de negócio |
| accstafunn.prw | CUSTOINFRA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Custo infraestrutura |
| accstafunn.prw | ISPERCME | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual ME |
| accstafunn.prw | FUNPERCZAY | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual ZAY |
| accstafunn.prw | FVALCDUTRC | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Valor CDU TRC |
| accstafunn.prw | FBASETROCA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Base de troca |
| accstafunn.prw | STATUSTROC | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Status de troca |
| accstafunn.prw | FVALBAIXAS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Valor de baixas |
| accstafunn.prw | PBASEBAIXA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual base baixa |
| accstafunn.prw | BAIXAPEND1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Baixa pendente 1 |
| accstafunn.prw | BAIXA90DIA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Baixa 90 dias |
| accstafunn.prw | FGERAABATM | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Gerar abatimento |
| accstafunn.prw | SMSGRETERR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | SMS gerente território |
| accstafunn.prw | REMTERRIRM | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Remover território IRM |
| accstafunn.prw | CHK180PEND | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar 180 pendente |
| accstafunn.prw | FABATSALDO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Abater saldo |
| accstafunn.prw | fGrvLogMem | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Gravar log em memória |
| accstafunn.prw | FGETZD8TER | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZD8 território |
| accstafunn.prw | ISEXCEPTIO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar exceção |
| accstafunn.prw | FGETZS6TER | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZS6 território |
| accstafunn.prw | ISEXCLUT | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar exclusão |
| accstafunn.prw | FADDTERRIT | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar território |
| accstafunn.prw | FCALCFATOR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Calcular fator |
| accstafunn.prw | FGETFATOR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter fator |
| accstafunn.prw | FGETVALZD2 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter valor ZD2 |
| accstafunn.prw | FISPREMEAR | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar prêmio AR |
| accstafunn.prw | FISTRIMEST | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar trimestre |
| accstafunn.prw | CMSGrvHist | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Gravar histórico CMS |
| accstafunn.prw | FISARRECIS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar arrecadação |
| accstafunn.prw | FCATCLIMDB | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Categoria cliente MDB |
| accstafunn.prw | FADDHUBAD2 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar hub AD2 |
| accstafunn.prw | TIPCOMSCS1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão SCS1 |
| accstafunn.prw | FEXNMETAOK | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar meta OK |
| accstafunn.prw | FGETSEGTER | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter segundo território |
| accstafunn.prw | ISEXCLUSEG | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar exclusão segundo |
| accstafunn.prw | SaldoZXO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Saldo ZXO |
| accstafunn.prw | FZXOSTATUS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Status ZXO |
| accstafunn.prw | FUNPSATCLI | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | PSA cliente |
| accstafunn.prw | BX180DIAS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Baixa 180 dias |
| accstafunn.prw | RECTIPCF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo CF20 |
| accstafunn.prw | RECTSPF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar SP F20 |
| accstafunn.prw | RTIPCDUF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo CDU F20 |
| accstafunn.prw | FOVER2015 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Over 2015 |
| accstafunn.prw | FANTIMP02 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Antecipação imposto 02 |
| accstafunn.prw | FROTOVER | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Rotina over |
| accstafunn.prw | FROTBASEL | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Rotina base L |
| accstafunn.prw | RECTIPCINT | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo C INT |
| accstafunn.prw | TMVAddOfer | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Adicionar oferta ao time de vendas |
| accstafunn.prw | FBSLPL5 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Base PL5 |
| accstafunn.prw | FBSLZXO | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Base ZXO |
| accstafunn.prw | CMSBSL001 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Base CMS 001 |
| accstafunn.prw | FUNPZD12 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual ZD12 |
| accstafunn.prw | RECNEWF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar new F20 |
| accstafunn.prw | GETCMSVD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter CMS vendedor |
| accstafunn.prw | EXMODBXVD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Executar modelo baixa vendedor |
| accstafunn.prw | RECTPCPCS | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo PCS |
| accstafunn.prw | FUNPZD13 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Percentual ZD13 |
| accstafunn.prw | FGETZD8ARG | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZD8 Argentina |
| accstafunn.prw | RTIPCOMF20 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão F20 |
| accstafunn.prw | GETPCOMMES | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter percentual comissão mês |
| accstafunn.prw | TMVGrpCli | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Time vendas grupo cliente |
| accstafunn.prw | RECTIPCPAD | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo comissão padrão |
| accstafunn.prw | CtrUsrRot | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Controle usuário rotina |
| accstafunn.prw | LockByTI | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Lock por TI |
| accstafunn.prw | UnLockTI | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Unlock TI |
| accstafunn.prw | GetAcesso | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter acesso |
| accstafunn.prw | GetDadEmp | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter dados empresa |
| accstafunn.prw | CRFUN01Z | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Criar função 01Z |
| accstafunn.prw | Arr2Str | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Array para string |
| accstafunn.prw | fGetComVal | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter valor comissão |
| accstafunn.prw | fGetZD1 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZD1 |
| accstafunn.prw | TRecLog | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Log de registro |
| accstafunn.prw | fZD1FAT | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | ZD1 faturamento |
| accstafunn.prw | COMREVEN | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Comissão receita |
| accstafunn.prw | FROTCON5 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Rotina CON5 |
| accstafunn.prw | VFYVLTAB | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Verificar valor tabela |
| accstafunn.prw | TIPCOM5 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Tipo comissão 5 |
| accstafunn.prw | fGetZD1C | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter ZD1C |
| accstafunn.prw | fGetCusPar | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Obter custo parceiro |
| accstafunn.prw | fVldEleg | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Validar elegível |
| accstafunn.prw | PFGetComVal | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | PF obter valor comissão |
| accstafunn.prw | RECTPCON | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Recuperar tipo CON |
| accstafunn.prw | QtdeParc | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Quantidade parcelas |
| accstafunn.prw | VLDEPARA | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | Validar de/para |
| accstafunn.prw | fDeParaDO2 | protheus_br/adm_vendas/Comissões/Environment Definition/Functions | De/Para DO2 |

## Observações
- A pasta Comissões contém um sistema complexo de cálculo de comissões e remuneração variável
- Muitas funções não possuem informações de versão específica nos comentários
- A subpasta RV (Remuneração Variável) contém as funções mais recentes com versões específicas
- As classes estão organizadas na subpasta Environment Definition/Classes
- Existem integrações com sistemas externos como PSA e ACTIO
- O sistema possui funcionalidades de importação, relatórios, webservices e rotinas de ajuste
- Total estimado de mais de 600 User Functions distribuídas em múltiplas subpastas
- Sistema modular com separação clara de responsabilidades por subpasta

## Resumo das Adições Realizadas

### Primeira Análise (Estrutura Geral)
- **Total de User Functions:** 600+ funções
- **Total de Classes:** 22 classes
- **Estrutura modular** com subpastas especializadas

### Segunda Adição - Environment Definition Functions
**Arquivos analisados:**
- `accsta01.prw` - 1 função principal de cálculo de provisão
- `accsta13.prw` - 12 funções de agendamento e processamento
- `accstafunn.prw` - 120+ funções auxiliares especializadas
- `accstxfcmp.prw` - 2 funções de execução de modelos

**Funções adicionadas:** 135+ funções especializadas em:
- Cálculos de comissão e percentuais
- Gestão de territórios e times de vendas
- Processamento de baixas e títulos
- Validações e verificações diversas
- Funções utilitárias (logs, conversões, etc.)
- Integrações com múltiplas tabelas (ZD1, ZD2, ZXO, ZPX, etc.)

### Terceira Adição - Funcoes Genericas
**Arquivos analisados:**
- `CMSXFUN.prw` - 9 funções genéricas expandidas
- `CRMImportTer.prw` - 1 função de importação de territórios
- `MXMECM.prw` - 1 função de integração ECM
- `TINTJ018.PRW` - 1 função de reenvio de JSON

**Funções detalhadas adicionadas:** 12 funções com descrições específicas:
- Geração de índices e tabelas (CMGerX5T, CMGerPBL)
- Processos de aprovação (CMRetApro, ADYTPROP)
- Funções de busca e obtenção de dados (GetLinRec, FGetSupN, FUndVinc, GetCmsEmp, BuscaGc)
- Importação de territórios (TerImport)
- Integração com sistemas externos (JobECMX, TINTJ018)

### Quarta Adição - Interfaces Functions (Atualização Atual)
**Subpastas analisadas:**
- `Interfaces/Classes` - 2 classes já documentadas (ACSTA44CLS, TdiCrtView)
- `Interfaces/Functions` - 100+ arquivos de interface

**Funções adicionadas:** 200+ funções organizadas em categorias:

**1. Principais Rotinas (ACCSTA02-43):** 50+ funções
- Processamento de provisões (ACCSTA02, ACCSTA03)
- Rotinas de manutenção e consulta (ACCSTA04-43)
- Funções de validação e filtros
- Processamento de dados ZPX, ZD2 e outras tabelas

**2. Cadastros e Manutenções (ACCSTA44-99):** 60+ funções
- Cadastros especializados (ACCSTA44-52)
- Rotinas de controle e aprovação (ACCSTA61-73)
- Funções de importação e exportação (ACCSTA80-99)
- Pontos de entrada e validações

**3. Manutenções Avançadas (ACCSTM01-54):** 80+ funções
- Manutenções de tabelas CMS (ACCSTM01-19)
- Processamento de objetos e modelos (ACCSTM34-54)
- Funções de validação e controle
- Integrações e aprovações

**4. CMS e Relatórios:** 30+ funções
- Relatórios especializados (ACCSTR05, ACCSTR46)
- Integrações CMS (CMINT001, CMSA001-140)
- Funções de legenda e visualização
- Processamento de dados CMS

### Principais subpastas documentadas:
- **Environment Definition** - Classes e funções base do sistema (135+ funções)
- **Funcoes Genericas** - Funções utilitárias e genéricas (12 funções detalhadas)
- **Interfaces** - Interfaces de usuário e funções de tela (200+ funções)
- **RV** - Remuneração Variável (sistema mais recente)
- **Relatorios** - Relatórios diversos
- **Rotinas Ajustes** - Rotinas de correção e ajuste
- **Webservices** - Serviços web
- **Procedures** - Procedures de banco

### Características principais identificadas:
- Sistema de Comissões complexo com múltiplas regras de negócio
- Remuneração Variável com integração PSA/ACTIO
- Classes organizadas para diferentes entidades (Cliente, Produto, Título, etc.)
- Versões específicas principalmente na subpasta RV (12.23.10, 12.1.33, etc.)
- Integrações externas com sistemas PSA e ACTIO
- Funcionalidades completas de importação, relatórios, webservices
- Sistema modular com separação clara de responsabilidades por subpasta
- Funções especializadas em regras de negócio de comissões
- Infraestrutura para processamento distribuído e multi-thread