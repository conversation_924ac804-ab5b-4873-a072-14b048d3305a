# Documentação das User Functions - Pasta Arena

## Resumo
Esta documentação apresenta todas as User Functions encontradas na pasta `adm_vendas/Arena` e suas respectivas subpastas.

## Tabela de User Functions

| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TRESA001.prw | TRESA001 | protheus_br/adm_vendas/Arena | Cadastro de Salas |
| TRESA002.prw | TRESA002 | protheus_br/adm_vendas/Arena | Cadastro de Tipos de Eventos |
| TRESA002.prw | TRESV002 | protheus_br/adm_vendas/Arena | Validacao do Campo |
| TRESA003.prw | TRESA003 | protheus_br/adm_vendas/Arena | Cadastro de Eventos |
| TRESA003.prw | TRES003C | 12.0 | Rotina que executa os filtros conforme a interação do usuário com o calendário |
| TRESA003.prw | TRESA03V | protheus_br/adm_vendas/Arena | Validacao de Campo |
| TRESA003.prw | TRESA03H | protheus_br/adm_vendas/Arena | Sugestão da hora inicial e final |
| TRESA003.prw | TRESA03P | protheus_br/adm_vendas/Arena | Validacao de Campo (cType = 1=Codigo ou 2=Codigo e Loja) |
| TRESA003.prw | TRSA03NC | protheus_br/adm_vendas/Arena | Inicialização do campo ACD_XNOMEC |
| TRESA003.prw | TRESA03W | protheus_br/adm_vendas/Arena | Validacao de Campo When Tabela ACD - Cliente/Suspect/Prospect e Lojas (pEntidade 1-Cliente, 2-Prospect 3-Suspect) |
| TRESA003.prw | TRESA03D | protheus_br/adm_vendas/Arena | Validacao de Campo Data para propor na banda filha |
| TRESA003.prw | TRESJ003 | protheus_br/adm_vendas/Arena | Rotina schedule de finalização dos eventos de acordo com a data base |
| TRESA004.PRW | TRESA004 | protheus_br/adm_vendas/Arena | Manutenção do Cadastro de Perguntas de Pesquisas |
| TRESA005.PRW | TRESA005 | protheus_br/adm_vendas/Arena | Manutenção do Cadastro de Equivalencias |
| TRESA006.prw | TRESA006 | protheus_br/adm_vendas/Arena | Manutenção das Respostas da Pesquisa Satisfação |
| TRESC001.PRW | TRESC001 | protheus_br/adm_vendas/Arena | Monta consulta padrão da tabela PIW agrupada pelo campo PIW_PESQ |
| TRESC002.PRW | TRESC002 | protheus_br/adm_vendas/Arena | Monta consulta padrão da tabela PGD agrupada pelo campo PKT_PONTUA |
| TRESM001.prw | TREST001 | protheus_br/adm_vendas/Arena | Função de teste para TRESM001 |
| TRESM001.prw | TRESM001 | protheus_br/adm_vendas/Arena | Envio de e-mail com as respostas da pesquisa de satisfação |
| TRESR003.prw | TRESR003 | protheus_br/adm_vendas/Arena | Relatório de Eventos da Arena Totvs |

## Observações
- A maioria das funções utiliza o caminho protheus_br/adm_vendas/Arena, exceto TRES003C que utiliza versão 12.0
- A maioria das funções foi desenvolvida entre julho e setembro de 2015
- Os principais autores são Fabio Carvalho e Wagner Mobile Costa
- As funções cobrem funcionalidades de cadastro, validação, consultas e relatórios para o sistema Arena Totvs
