# Documentação das User Functions - Pasta Arena

## Resumo
Esta documentação apresenta todas as User Functions encontradas na pasta `adm_vendas/Arena` e suas respectivas subpastas.

## Tabela de User Functions

| Arquivo | Função | Patch | Descrição |
|---------|--------|-------|-----------|
| TRESA001.prw | TRESA001 | P12 | Cadastro de Salas |
| TRESA002.prw | TRESA002 | P12 | Cadastro de Tipos de Eventos |
| TRESA002.prw | TRESV002 | P12 | Validacao do Campo |
| TRESA003.prw | TRESA003 | P12 | Cadastro de Eventos |
| TRESA003.prw | TRES003C | 12.0 | Rotina que executa os filtros conforme a interação do usuário com o calendário |
| TRESA003.prw | TRESA03V | P12 | Validacao de Campo |
| TRESA003.prw | TRESA03H | P12 | Sugestão da hora inicial e final |
| TRESA003.prw | TRESA03P | P12 | Validacao de Campo (cType = 1=Codigo ou 2=Codigo e Loja) |
| TRESA003.prw | TRSA03NC | P12 | Inicialização do campo ACD_XNOMEC |
| TRESA003.prw | TRESA03W | P12 | Validacao de Campo When Tabela ACD - Cliente/Suspect/Prospect e Lojas (pEntidade 1-Cliente, 2-Prospect 3-Suspect) |
| TRESA003.prw | TRESA03D | P12 | Validacao de Campo Data para propor na banda filha |
| TRESA003.prw | TRESJ003 | P12 | Rotina schedule de finalização dos eventos de acordo com a data base |
| TRESA004.PRW | TRESA004 | P12 | Manutenção do Cadastro de Perguntas de Pesquisas |
| TRESA005.PRW | TRESA005 | P12 | Manutenção do Cadastro de Equivalencias |
| TRESA006.prw | TRESA006 | P12 | Manutenção das Respostas da Pesquisa Satisfação |
| TRESC001.PRW | TRESC001 | P12 | Monta consulta padrão da tabela PIW agrupada pelo campo PIW_PESQ |
| TRESC002.PRW | TRESC002 | P12 | Monta consulta padrão da tabela PGD agrupada pelo campo PKT_PONTUA |
| TRESM001.prw | TREST001 | P12 | Função de teste para TRESM001 |
| TRESM001.prw | TRESM001 | P12 | Envio de e-mail com as respostas da pesquisa de satisfação |
| TRESR003.prw | TRESR003 | P12 | Relatório de Eventos da Arena Totvs |

## Detalhes por Arquivo

### TRESA001.prw
- **Autor:** Fabio Carvalho
- **Data:** 31/07/2015
- **Função Principal:** Cadastro de Salas
- **User Functions:**
  - `TRESA001()` - Função principal para cadastro de salas

### TRESA002.prw
- **Autor:** Fabio Carvalho
- **Data:** 03/08/2015
- **Função Principal:** Cadastro de Tipos de Eventos
- **User Functions:**
  - `TRESA002()` - Função principal para cadastro de tipos de eventos
  - `TRESV002()` - Validação de campo

### TRESA003.prw
- **Autor:** Fabio Carvalho / Wagner Mobile Costa
- **Data:** 05/08/2015
- **Função Principal:** Cadastro de Eventos
- **User Functions:**
  - `TRESA003()` - Função principal para cadastro de eventos
  - `TRES003C()` - Calendário com filtros interativos
  - `TRESA03V()` - Validação de campo
  - `TRESA03H()` - Sugestão de horários
  - `TRESA03P()` - Validação de campo com parâmetros
  - `TRSA03NC()` - Inicialização de campo
  - `TRESA03W()` - Validação When para entidades
  - `TRESA03D()` - Validação de data
  - `TRESJ003()` - Job de finalização de eventos

### TRESA004.PRW
- **Autor:** Wagner Mobile Costa
- **Data:** 28/08/2015
- **Função Principal:** Manutenção do Cadastro de Perguntas de Pesquisas
- **User Functions:**
  - `TRESA004()` - Cadastro de perguntas de pesquisas

### TRESA005.PRW
- **Autor:** Wagner Mobile Costa
- **Data:** 28/08/2015
- **Função Principal:** Manutenção do Cadastro de Equivalencias
- **User Functions:**
  - `TRESA005()` - Cadastro de equivalências

### TRESA006.prw
- **Autor:** Wagner Mobile Costa
- **Data:** 06/04/2015
- **Função Principal:** Manutenção das Respostas da Pesquisa Satisfação
- **User Functions:**
  - `TRESA006()` - Manutenção de respostas de pesquisa

### TRESC001.PRW
- **Autor:** Wagner Mobile Costa
- **Data:** 06/04/2015
- **Função Principal:** Consulta padrão da tabela PIW
- **User Functions:**
  - `TRESC001()` - Consulta agrupada por PIW_PESQ

### TRESC002.PRW
- **Autor:** Wagner Mobile Costa
- **Data:** 02/09/2015
- **Função Principal:** Consulta padrão da tabela PGD
- **User Functions:**
  - `TRESC002()` - Consulta agrupada por PKT_PONTUA

### TRESM001.prw
- **Autor:** Wagner Mobile Costa
- **Data:** 28/08/2015
- **Função Principal:** Envio de e-mail com respostas de pesquisa
- **User Functions:**
  - `TREST001()` - Função de teste
  - `TRESM001()` - Envio de e-mail principal

### TRESR003.prw
- **Autor:** Fabio Carvalho
- **Data:** 10/08/2015
- **Função Principal:** Relatório de Eventos da Arena Totvs
- **User Functions:**
  - `TRESR003()` - Geração de relatório de eventos

## Observações
- Todas as funções utilizam a versão P12 do Protheus, exceto TRES003C que utiliza versão 12.0
- A maioria das funções foi desenvolvida entre julho e setembro de 2015
- Os principais autores são Fabio Carvalho e Wagner Mobile Costa
- As funções cobrem funcionalidades de cadastro, validação, consultas e relatórios para o sistema Arena Totvs
