/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Objeto    ³CMSBxLiq()  ³Autor³Jailton B Santos-JBS   ³ Data ³10/09/2015³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Objeto responsavel pela criacao do objeto de baixa de um ti³±±
±±³          ³ tulo de liquidacao para calculo da previsao de comissao.   ³±±
±±³          ³                                                            ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Funcao    ³ Identifica todos os titulos originarios do titulo liquidado³±±
±±³          ³ e aplica sobre a soma doa valores, a proporcao recebida e  ³±±
±±³          ³ grava a resultado como comissao.                           ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ Especifico Financeiro Totvs S/A                            ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³         ATUALIZACOES SOFRIDAS DESDE A CONSTRU€AO INICIAL.             ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Programador ³ Data   ³  Motivo da Alteracao                            ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³            ³        ³                                                 ³±±
±±³            ³        ³                                                 ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
#INCLUDE "PROTHEUS.CH"
#INCLUDE "TOPCONN.CH"

//-- For Dummy
User Function _CMSBxLiq
Return Nil

// Criando a classe de Objeto CMSBxLiq

CLASS CMSBxLiq

	DATA aMsgErr	

	DATA cNumero
	DATA cPrefixo
	DATA cParcela
	DATA cCliente
	DATA cLoja
    DATA cSeqBx
    DATA cTipo
	DATA cMotBX
	DATA cDocumen
	DATA cBanco
	DATA cAgencia
	DATA cConta
	DATA cTipoDoc
	DATA cRecPag

	DATA dData
	DATA dVencto
	DATA dVencRea
	DATA dEmissTit

	DATA nValBaixa
	DATA nPropBaixa
	DATA nTxMoeda

	DATA lJuros
	DATA lMulta
	
	DATA nVlrRec
	DATA nCorrec
	DATA nDescont
	DATA nMulta
	DATA nJuros	
	
	DATA cNroLiq
	DATA nVlrLiq
	
	METHOD New(oTitulo, cSeqBX, cTipo, cTipoDoc) CONSTRUCTOR
	METHOD NewRecno(nRecno) CONSTRUCTOR
	METHOD NewDummy(oTitulo) CONSTRUCTOR
	METHOD Destruct()

ENDCLASS

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºObjeto    ³CMSBxLiq  ºAutor  ³Jailton B Santos-JBSº Data ³ 10/09/2015  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºMetodo    ³ New()                                                      º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³ Objeto responsavel pela criacao do objeto de propostas     º±±
±±º          ³ para calculo de comissoes Totvs                            º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºUso       ³ Totvs/Comissoes                                            º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/ 
METHOD New(oTitulo, cSeqBx, cTipo, cTipoDoc) CLASS CMSBxLiq

Local aArea		 := GetArea()
Local aAreaSE1  := SE1->( GetArea() )
Local aAreaSE5  := SE5->( GetArea() )
Local cTipBx    := cTipoDoc
Local cQuery    := ""
Local aLiquida  := {}
Local aQuerys   := {}
Local lPrimeira := .t. 
Local cFilSE5   := xFilial("SE5")
Local cPrefixo  
Local cNumero   
Local cParcela  
Local cTipo     
Local cCliFor   
Local cCliLoja  
Local cSeqBx 
Local cNaturez
Local nLeitura  := 1
Local nRecnoSE5 := 0

::aMsgErr		:= {}

::cNumero		:= oTitulo:cNumero
::cPrefixo 		:= oTitulo:cPrefixo
::cParcela		:= oTitulo:cParcela
::dEmissTit	    := oTitulo:dEmissao
::cSeqBx		:= cSeqBx
::cTipo			:= cTipo
::cTipoDoc		:= cTipoDoc

::dData			:= CtoD("  /  /  ")
::dVencto		:= CtoD("  /  /  ")
::dVencRea		:= CtoD("  /  /  ")

::cCliente		:= oTitulo:cCliente
::cLoja			:= oTitulo:cLoja
::cMotBX		:= ""
::cDocumen		:= ""
::cBanco		:= ""
::cAgencia		:= ""
::cConta		:= ""
::cRecPag		:= ""

::nValBaixa		:= 0
::nPropBaixa	:= 0
::nTxMoeda		:= 0

::nVlrRec		:= 0
::nCorrec		:= 0
::nDescont		:= 0
::nMulta		:= 0
::nJuros		:= 0

::lJuros		:= .F.
::lMulta		:= .F.

::cNroLiq      := ""
::nVlrLiq      := 0

cNaturez        := oTitulo:cNatureza
cPrefixo        := ::cPrefixo 
cNumero         := ::cNumero 
cParcela        := ::cParcela 
cTipo           := ::cTipo 
cCliFor         := ::cCliente 
cCliLoja        := ::cLoja 
cSeqBx          := ::cSeqBx

Do While .t.
	//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	//³Dados para localizar o titulo baixado³
	//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	cPrefixo  := SE1->E1_PREFIXO
	cNumero   := SE1->E1_NUM
	cParcela  := SE1->E1_PARCELA
	cTipo     := SE1->E1_TIPO
	//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	//³Localiza:                                                                                                  ³
	//³  => O titulo que foi liquidado que foi baixado                                                            ³
	//³  => Localizada o numero da liquidacao                                                                     ³
	//³  => Localiza o titulo Pai do liquidado                                                                    ³
	//³  => Na primeira vez pega o E5_DOCUMEN em branco, porque  na baixa da liquidação, este campo fica em branco³
	//³  => Na primeira filtra também a sequiencia da baixa, para pegar uma sequencia especifica.                 ³
	//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	cQuery := " Select SE5A.E5_NUMERO, "
	cQuery += "        SE5A.E5_PREFIXO, "
	cQuery += "        SE5A.E5_NATUREZ, "
	cQuery += "        SE5A.E5_DOCUMEN, "
	cQuery += "        SE5A.E5_VALOR, "
	cQuery += "        SE5A.E5_PARCELA, "
	cQuery += "        SE5A.E5_MOTBX, "
	cQuery += "        SE5A.E5_DOCUMEN, "
	cQuery += "        SE5A.E5_BANCO, "
	cQuery += "        SE5A.E5_AGENCIA, "
	cQuery += "        SE5A.E5_CONTA, "
	cQuery += "        SE5A.E5_RECPAG, "
	cQuery += "        SE5A.E5_DATA, "
	cQuery += "        SE5A.E5_VENCTO, "
	cQuery += "        SE5A.E5_DATA, "
	cQuery += "        SE5A.E5_TIPODOC, "
	cQuery += "        SE5A.E5_VLJUROS, "
	cQuery += "        SE5A.E5_VLMULTA, "
	cQuery += "        SE5A.E5_TXMOEDA, "
	cQuery += "        SE5A.E5_SEQ, "
	cQuery += "        SE5A.R_E_C_N_O_ SE5BRECNO, "
	
	cQuery += "        SE1A.E1_VENCREA E1AE1_VENCREA, "
	cQuery += "        SE1A.E1_NUMLIQ  E1AE1_NUMLIQ, "
	
	cQuery += "        SE1B.E1_NUM, "
	cQuery += "        SE1B.E1_PARCELA, "
	cQuery += "        SE1B.E1_PREFIXO, "
	cQuery += "        SE1B.E1_NATUREZ, "
	cQuery += "        SE1B.E1_VALOR, "
	cQuery += "        SE1B.E1_NUMLIQ, "
	cQuery += "        SE1B.E1_SALDO, "
	cQuery += "        SE1B.E1_BAIXA, "
	cQuery += "        SE1B.E1_VALLIQ, "
	cQuery += "        SE1B.R_E_C_N_O_ SE1BRECNO "
	
	cQuery += "   from " + RetSqlName("SE1") + " SE1A "
	
	cQuery += "  Inner join " + RetSqlName("SE5") + " SE5A "
	cQuery += "          on SE5A.E5_FILIAL  = SE1A.E1_FILIAL "
	cQuery += "         and SE5A.E5_NUMERO  = SE1A.E1_NUM "
	cQuery += "         and SE5A.E5_PREFIXO = SE1A.E1_PREFIXO "
	cQuery += "         and SE5A.E5_NATUREZ = SE1A.E1_NATUREZ "
	cQuery += "         and SE5A.E5_PARCELA = SE1A.E1_PARCELA "
	//
	// Precisa avaliar se realmente a parte nao prejudicara outras funcionalidades
	//
	If lPrimeira
	//	cQuery += "         and SE5A.E5_DOCUMEN = '' "
		cQuery += "         and SE5A.E5_SEQ = '"+cSeqBx+"'"
		lPrimeira := .f.
	EndIf
	
	cQuery += "         and ( SE5A.E5_RECPAG = 'R' Or (SE5A.E5_RECPAG = 'P' and (SE5A.E5_TIPODOC = 'ES' Or SE5A.E5_TIPO = 'NCC'))) "
	cQuery += "         and SE5A.D_E_L_E_T_ <>'*' "
	
	cQuery += "  Inner join " + RetSqlName("SE5") + " SE5B "
	cQuery += "          on SE5B.E5_FILIAL   = SE1A.E1_FILIAL "
	cQuery += "         and SE5B.E5_DOCUMEN  = SE1A.E1_NUMLIQ "
	cQuery += "         and SE5B.D_E_L_E_T_ <> '*' "
	
	cQuery += "  Inner join " + RetSqlName("SE1") + " SE1B "
	cQuery += "          on SE1B.E1_FILIAL  = SE5B.E5_FILIAL "
	cQuery += "         and SE1B.E1_NUM     = SE5B.E5_NUMERO "
	cQuery += "         and SE1B.E1_PREFIXO = SE5B.E5_PREFIXO "
	cQuery += "         and SE1B.E1_NATUREZ = SE5B.E5_NATUREZ "
	cQuery += "         and SE1B.E1_PARCELA = SE5B.E5_PARCELA "
	cQuery += "         and SE1B.D_E_L_E_T_ <> '*' "
	
	cQuery += "  where SE1A.E1_FILIAL  = '" + cFilSE5 + "' "
	cQuery += "    and SE1A.E1_NUM     = '" + cNumero + "' "
	cQuery += "    and SE1A.E1_PREFIXO = '" + cPrefixo + "' "
//	cQuery += "    and SE1A.E1_TIPO    = '" + cTipo + "' "
	cQuery += "    and SE1A.E1_NATUREZ = '" + cNaturez + "' "
	cQuery += "    and SE1A.E1_CLIENTE = '" + cCliFor + "' "
	cQuery += "    and SE1A.E1_LOJA    = '" + cCliLoja + "' "
	cQuery += "    and SE1A.E1_NATUREZ = '" + cNaturez + "' "
	cQuery += "    and SE1A.D_E_L_E_T_ <> '*' "
	//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	//³Na busca do titulo Pai original, podem ter ocorrido, entre este a baixa do titulo de liquidacao,                   ³
	//³inumeras reliquidacoes, desta forma forma o sistema cria as inumeras querys, e controla a primeira a ultima, sendo:³
	//³A primeira query:                                                                                                  ³
	//³ => Os dados com o valor da ultima baixa                                                                           ³
	//³ => Os dados ultimo titulo pai do titulo de liquidacao                                                             ³
	//³                                                                                                                   ³
	//³A Segunda query:                                                                                                   ³
	//³ => Os dados da primeira baixa                                                                                     ³
	//³ => Os dados do primeiro titulo Pai das liquidações e reliquidacoes                                                ³
	//³                                                                                                                   ³
	//³Para ter este comtrole, para cada query cria um novo alias, e administra todos estes atraves da array de querys    ³
	//³aQuerys                                                                                                            ³
	//³                                                                                                                   ³
	//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	cAliasSE1 := GetNextAlias()
    //ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
    //³Antes de criar a query verifica se o alias já existe, se existir fecha³
    //ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	If Select(cAliasSE1) > 0
		(cAliasSE1)->( DbCloseArea() )
	EndIf
	TcQuery cQuery New Alias &(cAliasSE1)
	
	TcSetField(cAliasSE1,"E1_VALOR"     ,"N",15,04)
	TcSetField(cAliasSE1,"E5_DATA"      ,"D",08,00)
	TcSetField(cAliasSE1,"E5_VENCTO"    ,"D",08,00)
	TcSetField(cAliasSE1,"E5_VALOR"     ,"N",15,04)
	TcSetField(cAliasSE1,"E1AE1_VENCREA" ,"D",08,00)
	TcSetField(cAliasSE1,"E5_VLJUROS"   ,"N",15,04) 
	TcSetField(cAliasSE1,"E5_VLMULTA"   ,"N",15,04)
		
	lRet := !(cAliasSE1)->(BOF().and.EOF())
	
	If lRet
		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³ Analisa se o registro titulo pai eh originado de outra processo de liquidacao,  ³
		//³ ou seja, eh de um processo de reliquidacao.                                     ³
		//³ Caso seja, repetir o processo ate chegar no primeiro titulo, o qual gerou o pri-³
		//³ meiro processo de liquidacao.                                                   ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	    aAdd(aQuerys,cAliasSE1)
		If Empty((cAliasSE1)->E1_NUMLIQ)
			Exit
		EndIf
		SE1->( DbGoTo((cAliasSE1)->SE1BRECNO) )
	Else
		(cAliasSE1)->( DbCloseArea() )
		Exit
	EndIf 
EndDo	
//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³aQuerys vai ser maior que zero, quando encontrar pelos menos um titulo Pai,     ³
//³desta forma segue para determinar o valor recebido e o total da soma dos titulos³
//³Pais que geraram a liquidacao que esta sendo baixada.                           ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
If len(aQuerys) > 0
	//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	//³Selecione a primeira query executada para pegar os valores do titulo baixado³
	//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	cAliasSE1 := aQuerys[1]                                                          '
    //ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
    //³Carrega os dados da baixa da liquidacao nas variaveis de memoria³
    //ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	::cMotBX		:= (cAliasSE1)->E5_MOTBX
	::cDocumen		:= (cAliasSE1)->E5_DOCUMEN
	::cBanco		:= (cAliasSE1)->E5_BANCO
	::cAgencia		:= (cAliasSE1)->E5_AGENCIA
	::cConta		:= (cAliasSE1)->E5_CONTA
	::cRecPag		:= (cAliasSE1)->E5_RECPAG
	If Empty(::cTipoDoc)
		::cTipoDoc := (cAliasSE1)->E5_TIPODOC
	EndIf
	::dData		   := (cAliasSE1)->E5_DATA
	::dVencto		:= (cAliasSE1)->E5_VENCTO
	::dVencRea		:= (cAliasSE1)->E1AE1_VENCREA
	::cNroLiq      := (cAliasSE1)->E1AE1_NUMLIQ
	::nVlrLiq      := 0
	
	nRecnoSE5      := 0
	
	Do While .t.
		
		Do While (cAliasSE1)->(!EOF())
			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³ Posicionando a Tabela "SE1" conforme o registro filtrado na Query          ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			SE1->( DbGoTo((cAliasSE1)->SE1BRECNO) )
			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³ So apura os valores da baixa (SE5), na primeira leitura                    ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			If nLeitura == 1
				//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
				//³ Na leitura da Query pega os valors filtrados do SE5 apenas quando for um   ³
				//³ um novo registro no SE5, este tratamento existe porque a query vai repetir ³
				//³ a mesma linha do SE5 para cadas SE1 PAI. Por exemplo: 3 pais diferentes    ³
				//³ O filho baixado no SE5, aparecerao 3 linhas iguais                         ³
				//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
				If nRecnoSE5 <> (cAliasSE1)->SE5BRECNO
					//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
					//³ Posicione a tabela oficial SE5 confome o que foi retornado pela query      ³
					//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
					SE5->( DbGoTo((cAliasSE1)->SE5BRECNO) )
					
					If cPaisLoc <> "BRA"
						::nTxMoeda := SE5->E5_TXMOEDA
					EndIf
					
					// A provisao da Argentina foi importada com base no Saldo no SE1(setembro/2010), por isso nao podemos gerar o ZPX de baixas anteriores a outubro/2010
					If cPaisLoc == "ARG"
						If DTOS((cAliasSE1)->E5_DATA) < "20101001"
							(cliasSE1)->(dbSkip())
							Loop
						EndIf
					EndIf
					
					// Se houver passagem de parametro do Tipo de Documento, desconsiderar todos os tipos.
					
					If !Empty(cTipBx) .And. !(AllTrim((cAliasSE1)->E5_TIPODOC) $ cTipBx)
						(cAliasSE1)->(dbSkip())
						Loop
					EndIf
					
					If ( (cAliasSE1)->E5_TIPODOC $ "VL#BA#V2#CP#LJ" )
						::nVlrRec  += (cAliasSE1)->E5_VALOR
					Endif
					
					If ( (cAliasSE1)->E5_TIPODOC $ "DB" )
						::nVlrRec -= (cAliasSE1)->E5_VALOR
					Endif
					
					If ( (cAliasSE1)->E5_TIPODOC $ "CM#C2#CX" )
						::nCorrec += (cAliasSE1)->E5_VALOR
					Endif
					
					If ( (cAliasSE1)->E5_TIPODOC $ "DC#D2" )
						::nDescont += (cAliasSE1)->E5_VALOR
					Endif
					
					If ( (cAliasSE1)->E5_TIPODOC $ "MT#M2" )
						::nMulta  += (cAliasSE1)->E5_VALOR
					Endif
					
					If ( SE5->E5_TIPODOC $ "JR#J2" )
						::nJuros  += (cAliasSE1)->E5_VALOR
					EndIf
					
					If ( (cAliasSE1)->E5_TIPODOC $ "ES" ) .And. !Empty(cTipBx) .And. AllTrim(cTipBx) $ "ES"
						::nVlrRec  -= (cAliasSE1)->E5_VALOR - (cAliasSE1)->E5_VLJUROS - (cAliasSE1)->E5_VLMULTA
					EndIf
					
					nRecnoSE5 := (cAliasSE1)->SE5BRECNO
					
				EndIf
			EndIf
			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³Somar os valores dos titulos Pais para aplicar a proporcao do ³
			//³que foi pago.                                                 ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			If nLeitura == len(aQuerys)
				::nVlrLiq += (cAliasSE1)->E1_VALOR
			EndIf
			
			(cAliasSE1)->( DbSkip() )
			
		EndDo
		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³Quando a leitura atual for a ultima leitura abandona o laco de  ³
		//³repeticao, quando nao existir reliquidacao ou estiver na ultima ³
		//³leitura, ultima query, titulo pai original                      ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		If nLeitura == len(aQuerys)
			Exit
		EndIf
		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³Quando houverem reliquidacoes de titulos, houveram inumeras        ³
		//³querys geradas. Interessa a primeira, que é a dos valores recebidos³
		//³No SE5 e a ultima, que sao os valores dos titulos pais somados     ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		If nLeitura < len(aQuerys)
			nLeitura := len(aQuerys)       // Passa para ultima leitura
			cAliasSE1 := aQuerys[nLeitura] // Passa para ultima query criada                                                         '
		EndIf
	EndDo

	If !::lMulta
		::nVlrRec -= ::nMulta
	EndIf
	
	If !::lJuros
		::nVlrRec -= ::nJuros
	EndIf
	
	If cPaisLoc <> "BRA"
		If ::nTxMoeda <> 0
			::nVlrRec := ::nVlrRec * ::nTxMoeda
			::nVlrLiq := ::nVlrLiq * ::nTxMoeda
		EndIf
	EndIf
	//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	//³ Calculo da proporcao da liquidacao baixada          ³
	//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	//::nPropBaixa := ::nVlrRec / (oTitulo:nSaldoTit - oTitulo:nSomaAbat)
	::nPropBaixa := ::nVlrRec / ::nVlrLiq
EndIf
//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ Eliminar todas as querys criadas durante o processo ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
aEval(aQuerys,{|x| (x)->( DbCloseArea() )})

SE5->(RestArea(aAreaSE5))
SE1->(RestArea(aAreaSE1))
RestArea(aArea)
Return Self
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºObjeto    ³CMSBxLiq  ºAutor  ³Jailton B Santos-JBSº Data ³ 10/09/2015  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºMetodo    ³ NewRecno()                                                 º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³ Objeto responsavel pela criacao do objeto de titulos       º±±
±±º          ³ por recno para calculo de comissoes Totvs                  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºUso       ³ Totvs/Comissoes                                            º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/ 
METHOD NewRecno(oTitulo, nRecno) CLASS CMSBxLiq

Local oRet
Local aAreaSE5	:= {}
Local aArea		:= GetArea()

dbSelectArea("SE5")
aAreaSE5 := SE5->(GetArea())

SE5->(dbGoTo(nRecno))

oRet := ::New(oTitulo, SE5->E5_SEQBX, SE5->E5_TIPO)

SE5->(RestArea(aAreaSE5))
RestArea(aArea)
Return oRet
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºObjeto    ³CMSBxLiq  ºAutor  ³Jailton B Santos-JBSº Data ³ 10/09/2015  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºMetodo    ³ NewDummy()                                                 º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³ Objeto responsavel pela criacao do objeto de baixas        º±±
±±º          ³ sem existir uma baixa (usado para compatibilizar env.)     º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºUso       ³ Totvs/Comissoes                                            º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/ 
METHOD NewDummy(oTitulo) CLASS CMSBxLiq

Local aArea		:= GetArea()

::aMsgErr		:= {}

::cNumero		:= oTitulo:cNumero
::cPrefixo 		:= oTitulo:cPrefixo
::cParcela		:= oTitulo:cParcela
::cSeqBx		:= 0
::cTipo			:= oTitulo:cTipo
::dEmissTit	    := oTitulo:dEmissao
::dData			:= oTitulo:dEmissao
::dVencto		:= oTitulo:dVencto
::dVencRea		:= oTitulo:dVencRea

::cCliente		:= oTitulo:cCliente
::cLoja			:= oTitulo:cLoja
::cMotBX		:= "ABT"
::cDocumen		:= ""
::cBanco		:= ""
::cAgencia		:= ""
::cConta		:= ""

::cTipoDoc		:= ""
::cRecPag		:= ""

::nValBaixa		:= 0
::nPropBaixa	:= 0
::nTxMoeda		:= 0

::nVlrRec		:= 0
::nCorrec		:= 0
::nDescont		:= 0
::nMulta		:= 0
::nJuros		:= 0

::lJuros		:= .F.
::lMulta		:= .F.

::cNroLiq      := ""
::nVlrLiq      := 0

RestArea( aArea )

Return
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºObjeto    ³CMSBxLiq  ºAutor  ³Jailton B Santos-JBSº Data ³ 10/09/2015  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºMetodo    ³ Destruct()                                                 º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³ Metodo responsavel pela eliminacao de referencias a objetosº±±
±±º          ³ antes da eliminacao da instancia do objeto                 º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºUso       ³ Totvs/Comissoes                                            º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/ 
METHOD Destruct() CLASS CMSBxLiq
	::aMsgErr := Nil
Return